# Hướng dẫn tạo Theme cho ứng dụng dự toán xây dựng Flutter

## Giới thiệu

Tài liệu này hướng dẫn cách tạo file theme cho ứng dụng Flutter về tính toán dự toán khối lượng và chi phí xây dựng. Theme này được thiết kế theo phong cách hiện đại, sáng với các thẻ màu pastel và hiển thị dữ liệu rõ ràng.

## Cấu trúc theme

File theme được tổ chức thành hai lớp chính:
1. `ConstructionTheme`: Chứa các màu sắ<PERSON>, phông chữ và các định nghĩa theme cơ bản
2. `CustomWidgets`: Chứa các widget tùy chỉnh phù hợp với theme

## Hướng dẫn từng bước

### Bước 1: Tạo file theme mới

Tạo file mới có tên `construction_theme.dart` trong thư mục `lib/theme/` của dự án Flutter của bạn.

### Bước 2: Import các package cần thiết

```dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
```

### Bước 3: Định nghĩa lớp ConstructionTheme

Lớp này sẽ chứa tất cả các màu sắc, phông chữ và theme cơ bản:

1. **Định nghĩa màu sắc**:
   - Màu chính (primaryColor, secondaryColor, accentColor)
   - Màu nền (backgroundColor)
   - Màu thẻ (cardGreenColor, cardBlueColor, cardPurpleColor, cardYellowColor)
   - Màu văn bản (textPrimaryColor, textSecondaryColor, textLightColor)
   - Màu gradient

2. **Định nghĩa phông chữ**:
   - Sử dụng fontFamily: 'Poppins'

3. **Tạo lightTheme**:
   - Định nghĩa AppBarTheme
   - Định nghĩa TextTheme
   - Định nghĩa CardTheme
   - Định nghĩa các theme cho nút (ElevatedButton, OutlinedButton, TextButton)
   - Định nghĩa InputDecorationTheme
   - Định nghĩa BottomNavigationBarTheme
   - Định nghĩa ColorScheme

### Bước 4: Tạo lớp CustomWidgets

Lớp này chứa các widget tùy chỉnh phù hợp với theme:

1. **gradientCard**: Card với nền gradient hiển thị các chỉ số quan trọng
2. **metricCard**: Thẻ hiển thị các chỉ số với nền đơn sắc
3. **circleButton**: Nút tròn với icon
4. **costInfoCard**: Thẻ hiển thị thông tin chi phí với khả năng hiển thị thay đổi

### Bước 5: Áp dụng theme vào ứng dụng

Trong file `main.dart` của bạn:

```dart
import 'package:flutter/material.dart';
import 'package:your_app/theme/construction_theme.dart';

void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Ứng dụng dự toán xây dựng',
      theme: ConstructionTheme.lightTheme,
      home: HomeScreen(),
    );
  }
}
```

## Bảng màu

| Thành phần | Mã màu | Mô tả |
|------------|---------|-------|
| primaryColor | 0xFF6B8BC9 | Xanh dương chủ đạo |
| secondaryColor | 0xFFFFB27D | Cam nhẹ |
| accentColor | 0xFFA9D4D9 | Xanh lam nhạt |
| backgroundColor | 0xFFF5F5FA | Xám nhạt làm nền |
| cardGreenColor | 0xFFB8E2D0 | Xanh lá nhạt cho thẻ |
| cardBlueColor | 0xFFB8E2EC | Xanh dương nhạt cho thẻ |
| cardPurpleColor | 0xFFD4C7E7 | Tím nhạt cho thẻ |
| cardYellowColor | 0xFFFFF4D8 | Vàng nhạt cho thẻ |

## Hướng dẫn sử dụng các widget tùy chỉnh

### 1. Sử dụng gradientCard

```dart
CustomWidgets.gradientCard(
  title: 'Chi phí',
  value: '24.5M',
  gradientColors: ConstructionTheme.primaryGradient,
  icon: Icon(Icons.trending_up, color: Colors.black54),
  subtitle: 'VND/m²',
)
```

### 2. Sử dụng metricCard

```dart
CustomWidgets.metricCard(
  title: 'Khối lượng bê tông',
  value: '125.4',
  backgroundColor: ConstructionTheme.cardBlueColor,
  icon: Icons.category,
  subtitle: 'm³',
)
```

### 3. Sử dụng circleButton

```dart
CustomWidgets.circleButton(
  icon: Icons.add,
  onPressed: () {
    // Xử lý khi nhấn nút
  },
)
```

### 4. Sử dụng costInfoCard

```dart
CustomWidgets.costInfoCard(
  title: 'Tổng chi phí ước tính',
  amount: '358.7',
  unit: 'triệu',
  change: '+28%',
  backgroundColor: ConstructionTheme.cardGreenColor,
)
```

## Lưu ý quan trọng

1. **Đảm bảo sự nhất quán**: Luôn sử dụng các màu từ `ConstructionTheme` thay vì hardcode màu sắc.

2. **Tùy chỉnh theo nhu cầu**: Bạn có thể mở rộng các lớp này với các màu sắc hoặc widget tùy chỉnh khác phù hợp với nhu cầu cụ thể của ứng dụng.

3. **Phông chữ**: Đảm bảo bạn đã khai báo phông chữ Poppins trong file `pubspec.yaml`:
   ```yaml
   fonts:
     - family: Poppins
       fonts:
         - asset: assets/fonts/Poppins-Regular.ttf
         - asset: assets/fonts/Poppins-Medium.ttf
           weight: 500
         - asset: assets/fonts/Poppins-SemiBold.ttf
           weight: 600
         - asset: assets/fonts/Poppins-Bold.ttf
           weight: 700
   ```

4. **Responsive design**: Các widget tùy chỉnh đều có tham số width tùy chọn để thích ứng với các kích thước màn hình khác nhau.

## Ví dụ màn hình trang chủ

Dưới đây là một ví dụ về cách tạo trang chủ đơn giản sử dụng theme và các widget tùy chỉnh:

```dart
class HomeScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Dự toán xây dựng'),
        actions: [
          IconButton(
            icon: Icon(Icons.settings),
            onPressed: () {},
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Tổng quan dự án',
              style: Theme.of(context).textTheme.displaySmall,
            ),
            SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: CustomWidgets.metricCard(
                    title: 'Diện tích',
                    value: '245',
                    backgroundColor: ConstructionTheme.cardBlueColor,
                    subtitle: 'm²',
                  ),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: CustomWidgets.metricCard(
                    title: 'Tiến độ',
                    value: '58%',
                    backgroundColor: ConstructionTheme.cardPurpleColor,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16),
            CustomWidgets.costInfoCard(
              title: 'Tổng chi phí dự toán',
              amount: '1.35',
              unit: 'tỷ VND',
              change: '+12% so với dự kiến',
            ),
            SizedBox(height: 24),
            Text(
              'Chi tiết vật liệu',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            SizedBox(height: 16),
            // Thêm danh sách chi tiết vật liệu ở đây
          ],
        ),
      ),
      floatingActionButton: CustomWidgets.circleButton(
        icon: Icons.add,
        onPressed: () {},
        backgroundColor: ConstructionTheme.primaryColor,
        iconColor: Colors.white,
      ),
      bottomNavigationBar: BottomNavigationBar(
        items: [
          BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Trang chủ'),
          BottomNavigationBarItem(icon: Icon(Icons.calculate), label: 'Dự toán'),
          BottomNavigationBarItem(icon: Icon(Icons.bar_chart), label: 'Báo cáo'),
          BottomNavigationBarItem(icon: Icon(Icons.settings), label: 'Cài đặt'),
        ],
        currentIndex: 0,
      ),
    );
  }
}
```

## Kết luận

Bằng cách tuân theo hướng dẫn này, bạn có thể tạo một theme nhất quán và hấp dẫn cho ứng dụng dự toán xây dựng Flutter của mình. Theme này không chỉ giúp giao diện người dùng trông đẹp mắt mà còn tạo cảm giác chuyên nghiệp và dễ sử dụng.