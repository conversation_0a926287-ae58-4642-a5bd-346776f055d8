# Hướng Dẫn chỉnh sửa tối ưu Ứng Dụng lần thứ 4.1

## 1. Giới Thiệu
Ở những lần chỉnh sửa trước chúng ta tối ưu luồng wizard tương đối hoàn chỉnh nhưng vẫn thiếu bước tiếp theo trong luồng. Ở lần chỉnh sửa tối ưu này hãy thêm một bước nữa sau bước Lựa Chọn Vật Liệu.

### Bước tiếp theo: Nhập Thông Số Chi Tiết
- **Mục đích**: <PERSON>hu thập thông số kỹ thuật chi tiết để tính toán khối lượng và chi phí.
- **Thông tin cần nhập**:
  - **Móng**: <PERSON><PERSON><PERSON> dà<PERSON> (m), số c<PERSON>t trụ (nếu có).
  - **Tường**: <PERSON><PERSON><PERSON> tích các loại tườ<PERSON> (m²).
  - **Cửa**: <PERSON><PERSON><PERSON> tích cửa sổ, c<PERSON><PERSON> đ<PERSON>, cử<PERSON> cuốn (m²).
  - **Khác**: <PERSON><PERSON><PERSON> v<PERSON>h (số lượng), trần thạch cao (m²), cầu thang (số bậc).
- **Tối ưu UX**:
  - Trường nhập số kèm đơn vị rõ ràng (m, m²).
  - Cung cấp giá trị mặc định hoặc gợi ý dựa trên diện tích đã nhập.

## 3. Tính Toán và Xuất Kết Quả
- **Quy trình tính toán**:
  - Tính khối lượng nhân công và vật liệu dựa trên thông tin từ luồng wizard tổng hợp các bước trên.
  - Tính chi phí tổng dựa trên khối lượng và đơn giá (đơn giá có thể lấy từ cơ sở dữ liệu mặc định hoặc nhập tay).
- **Xuất kết quả**:
  - **Biểu đồ**: Pie chart hiển thị tỷ trọng chi phí (nhân công, vật liệu, khác).
  - **Bảng chi tiết**: Liệt kê khối lượng, đơn giá, thành tiền; kèm nút "Sửa" để chỉnh sửa từng mục.
  - **Tổng chi phí**: Hiển thị tổng chi phí rõ ràng, tổng chi phí là công thức tính tổng các hạng mục đã tính toán được ở Bảng chi tiết.
  - **Tùy chọn**: Xuất file PDF, chia sẻ qua email, hoặc lưu trữ local.