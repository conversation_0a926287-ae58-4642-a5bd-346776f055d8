import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/material_provider.dart';
import '../models/material_model.dart' as model;
import '../models/brick.dart';
import '../utils/wall_calculator.dart';
import '../utils/number_formatter.dart';

/// Tab cho việc thêm nhà và tính toán vật liệu
class AddHouseTab extends StatefulWidget {
  const AddHouseTab({super.key});

  @override
  State<AddHouseTab> createState() => _AddHouseTabState();
}

class _AddHouseTabState extends State<AddHouseTab> {
  final _formKey = GlobalKey<FormState>();
  
  // Các controller cho các trường nhập liệu
  final TextEditingController _lengthController = TextEditingController();
  final TextEditingController _heightController = TextEditingController();
  
  // <PERSON>ại tường đư<PERSON><PERSON> chọn
  <PERSON>Type _selectedWallType = WallType.wall10cm1Side;
  
  // <PERSON>ết quả tính toán
  Map<String, dynamic> _wallParameters = {};
  Map<String, Map<String, dynamic>> _calculationResults = {};
  bool _hasCalculated = false;

  @override
  void dispose() {
    _lengthController.dispose();
    _heightController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<MaterialProvider>(
      builder: (context, materialProvider, child) {
        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Thêm nhà mới:',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),
                
                // Trường nhập chiều dài
                TextFormField(
                  controller: _lengthController,
                  decoration: const InputDecoration(
                    labelText: 'Chiều dài tường',
                    border: OutlineInputBorder(),
                    suffixText: 'm',
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Vui lòng nhập chiều dài';
                    }
                    if (double.tryParse(value) == null) {
                      return 'Vui lòng nhập số hợp lệ';
                    }
                    return null;
                  },
                ),
                
                const SizedBox(height: 16),
                
                // Trường nhập chiều cao
                TextFormField(
                  controller: _heightController,
                  decoration: const InputDecoration(
                    labelText: 'Chiều cao tường',
                    border: OutlineInputBorder(),
                    suffixText: 'm',
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Vui lòng nhập chiều cao';
                    }
                    if (double.tryParse(value) == null) {
                      return 'Vui lòng nhập số hợp lệ';
                    }
                    return null;
                  },
                ),
                
                const SizedBox(height: 16),
                
                // Dropdown chọn loại tường
                DropdownButtonFormField<WallType>(
                  decoration: const InputDecoration(
                    labelText: 'Loại tường',
                    border: OutlineInputBorder(),
                  ),
                  value: _selectedWallType,
                  items: [
                    DropdownMenuItem(
                      value: WallType.wall10cm1Side,
                      child: const Text('Tường 10cm chát 1 mặt'),
                    ),
                    DropdownMenuItem(
                      value: WallType.wall10cm2Side,
                      child: const Text('Tường 10cm chát 2 mặt'),
                    ),
                    DropdownMenuItem(
                      value: WallType.wall20cm1Side,
                      child: const Text('Tường 20cm chát 1 mặt'),
                    ),
                    DropdownMenuItem(
                      value: WallType.wall20cm2Side,
                      child: const Text('Tường 20cm chát 2 mặt'),
                    ),
                  ],
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _selectedWallType = value;
                      });
                    }
                  },
                ),
                
                const SizedBox(height: 24),
                
                // Nút tính toán
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      if (_formKey.currentState!.validate()) {
                        _calculateWallMaterials(materialProvider);
                      }
                    },
                    child: const Padding(
                      padding: EdgeInsets.all(12.0),
                      child: Text(
                        'Tính toán vật liệu',
                        style: TextStyle(fontSize: 16),
                      ),
                    ),
                  ),
                ),
                
                const SizedBox(height: 24),
                
                // Hiển thị kết quả
                if (_hasCalculated) _buildResultsSection(materialProvider),
              ],
            ),
          ),
        );
      },
    );
  }
  
  /// Tính toán vật liệu cho tường
  void _calculateWallMaterials(MaterialProvider materialProvider) {
    // Lấy các giá trị từ form
    final double length = double.parse(_lengthController.text);
    final double height = double.parse(_heightController.text);
    
    // Tính toán các thông số tường
    _wallParameters = WallCalculator.calculateWallParameters(
      wallType: _selectedWallType,
      length: length,
      height: height,
    );
    
    // Tính toán vật liệu
    _calculationResults = {};
    
    // Tìm các vật liệu cần thiết
    final brick = materialProvider.materials.firstWhere(
      (m) => m.type == model.MaterialType.brick,
    );
    
    final sand = materialProvider.materials.firstWhere(
      (m) => m.type == model.MaterialType.sand && m.name == 'Cát xây',
    );
    
    final plasteringSand = materialProvider.materials.firstWhere(
      (m) => m.type == model.MaterialType.sand && m.name == 'Cát trát',
      orElse: () => materialProvider.materials.firstWhere(
        (m) => m.type == model.MaterialType.sand,
      ),
    );
    
    final cement = materialProvider.materials.firstWhere(
      (m) => m.type == model.MaterialType.cement,
    );
    
    // Tính toán và lưu kết quả
    _calculationResults[brick.name] = {
      'material': brick,
      'quantity': brick.calculateQuantity(_wallParameters),
      'cost': brick.calculateCost(_wallParameters),
    };
    
    _calculationResults[sand.name] = {
      'material': sand,
      'quantity': sand.calculateQuantity(_wallParameters),
      'cost': sand.calculateCost(_wallParameters),
    };
    
    _calculationResults[plasteringSand.name] = {
      'material': plasteringSand,
      'quantity': plasteringSand.calculateQuantity(_wallParameters),
      'cost': plasteringSand.calculateCost(_wallParameters),
    };
    
    _calculationResults[cement.name] = {
      'material': cement,
      'quantity': cement.calculateQuantity(_wallParameters),
      'cost': cement.calculateCost(_wallParameters),
    };
    
    setState(() {
      _hasCalculated = true;
    });
  }
  
  /// Xây dựng phần hiển thị kết quả
  Widget _buildResultsSection(MaterialProvider materialProvider) {
    // Tính tổng chi phí
    double totalCost = 0;
    for (final result in _calculationResults.values) {
      totalCost += result['cost'] as double;
    }
    
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Kết quả tính toán:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            
            // Thông tin tường
            Text('Diện tích tường: ${NumberFormatter.format(_wallParameters['wallArea'])} m²'),
            Text('Thể tích tường: ${NumberFormatter.format(_wallParameters['wallVolume'])} m³'),
            
            const Divider(),
            
            // Danh sách vật liệu
            ..._calculationResults.entries.map((entry) {
              final material = entry.value['material'] as model.Material;
              final quantity = entry.value['quantity'] as double;
              final cost = entry.value['cost'] as double;
              
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 4.0),
                child: Row(
                  children: [
                    Expanded(
                      flex: 3,
                      child: Text(material.name),
                    ),
                    Expanded(
                      flex: 2,
                      child: Text('${NumberFormatter.format(quantity)} ${material.unit}'),
                    ),
                    Expanded(
                      flex: 3,
                      child: Text('${NumberFormatter.formatCurrency(cost)} VNĐ'),
                    ),
                  ],
                ),
              );
            }),
            
            const Divider(),
            
            // Tổng chi phí
            Row(
              children: [
                const Expanded(
                  flex: 5,
                  child: Text(
                    'Tổng chi phí:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
                Expanded(
                  flex: 3,
                  child: Text(
                    '${NumberFormatter.formatCurrency(totalCost)} VNĐ',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
