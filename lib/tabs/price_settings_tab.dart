import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/material_provider.dart';
import '../models/material_model.dart' as model;
import '../services/price_storage_service.dart';
import '../utils/number_formatter.dart';

/// Tab cho việc cài đặt giá vật liệu
class PriceSettingsTab extends StatefulWidget {
  const PriceSettingsTab({super.key});

  @override
  State<PriceSettingsTab> createState() => _PriceSettingsTabState();
}

class _PriceSettingsTabState extends State<PriceSettingsTab> {
  final PriceStorageService _priceStorageService = PriceStorageService();
  final Map<String, TextEditingController> _controllers = {};
  final Map<String, String> _formattedPrices = {}; // Lưu giá đã định dạng
  bool _isSaving = false; // Trạng thái đang lưu

  // Controllers cho việc thêm vật liệu mới
  final TextEditingController _newMaterialNameController =
      TextEditingController();
  final TextEditingController _newMaterialPriceController =
      TextEditingController();
  final TextEditingController _newMaterialUnitController =
      TextEditingController();

  // Đơn vị đo lường cho vật liệu mới
  model.MeasurementUnit _selectedMeasurementUnit = model.MeasurementUnit.piece;

  @override
  void dispose() {
    // Giải phóng tất cả các controller
    for (final controller in _controllers.values) {
      controller.dispose();
    }
    _newMaterialNameController.dispose();
    _newMaterialPriceController.dispose();
    _newMaterialUnitController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<MaterialProvider>(
      builder: (context, materialProvider, child) {
        // Khởi tạo các controller cho mỗi vật liệu nếu chưa được thực hiện
        for (final material in materialProvider.materials) {
          if (!_controllers.containsKey(material.name)) {
            // Định dạng giá theo phần nghìn
            final formattedPrice = NumberFormatter.formatCurrency(
              material.pricePerUnit,
            );
            _formattedPrices[material.name] = formattedPrice;

            _controllers[material.name] = TextEditingController(
              text: formattedPrice,
            );
          }
        }

        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Cài đặt giá vật liệu:',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),

              // Danh sách vật liệu hiện có
              Expanded(
                child: ListView.builder(
                  itemCount: materialProvider.materials.length,
                  itemBuilder: (context, index) {
                    final material = materialProvider.materials[index];
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 16.0),
                      child: Row(
                        children: [
                          Expanded(
                            flex: 3,
                            child: Text(
                              material.name,
                              style: const TextStyle(fontSize: 16),
                            ),
                          ),
                          Expanded(
                            flex: 4,
                            child: TextField(
                              controller: _controllers[material.name],
                              decoration: InputDecoration(
                                border: const OutlineInputBorder(),
                                suffixText: 'VNĐ/${material.unit}',
                                contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 8,
                                ),
                              ),
                              keyboardType: TextInputType.number,
                              onChanged: (value) {
                                // Khi người dùng nhập, chuyển đổi về định dạng số thường
                                final numericValue = _parseFormattedNumber(
                                  value,
                                );
                                if (numericValue != null) {
                                  // Định dạng lại số với dấu phân cách hàng nghìn
                                  final formattedValue =
                                      NumberFormatter.formatCurrency(
                                        numericValue,
                                      );

                                  // Cập nhật controller nếu định dạng khác với giá trị hiện tại
                                  if (formattedValue != value) {
                                    // Lưu vị trí con trỏ hiện tại
                                    final cursorPos =
                                        _controllers[material.name]!
                                            .selection
                                            .start;

                                    // Tính toán vị trí con trỏ mới (số ký tự đã thêm)
                                    final additionalChars =
                                        formattedValue.length - value.length;

                                    // Cập nhật text và vị trí con trỏ
                                    _controllers[material.name]!
                                        .value = TextEditingValue(
                                      text: formattedValue,
                                      selection: TextSelection.collapsed(
                                        offset:
                                            cursorPos +
                                            (additionalChars > 0
                                                ? additionalChars
                                                : 0),
                                      ),
                                    );
                                  }
                                }
                              },
                            ),
                          ),
                          // Nút xóa chỉ hiển thị cho vật liệu tùy chỉnh
                          if (material.type == model.MaterialType.custom)
                            IconButton(
                              icon: const Icon(Icons.delete, color: Colors.red),
                              onPressed: () {
                                materialProvider.removeMaterial(material);
                                _controllers.remove(material.name);
                                _formattedPrices.remove(material.name);
                              },
                            ),
                        ],
                      ),
                    );
                  },
                ),
              ),

              const Divider(),

              // Phần thêm vật liệu mới
              ExpansionTile(
                title: const Text(
                  'Thêm vật liệu mới',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                children: [
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Column(
                      children: [
                        // Tên vật liệu
                        TextField(
                          controller: _newMaterialNameController,
                          decoration: const InputDecoration(
                            labelText: 'Tên vật liệu',
                            border: OutlineInputBorder(),
                          ),
                        ),

                        const SizedBox(height: 12),

                        // Giá vật liệu
                        TextField(
                          controller: _newMaterialPriceController,
                          decoration: const InputDecoration(
                            labelText: 'Giá vật liệu',
                            border: OutlineInputBorder(),
                            prefixText: 'VNĐ ',
                          ),
                          keyboardType: TextInputType.number,
                          onChanged: (value) {
                            // Định dạng số khi nhập
                            final numericValue = _parseFormattedNumber(value);
                            if (numericValue != null) {
                              final formattedValue =
                                  NumberFormatter.formatCurrency(numericValue);
                              if (formattedValue != value) {
                                final cursorPos =
                                    _newMaterialPriceController.selection.start;
                                final additionalChars =
                                    formattedValue.length - value.length;

                                _newMaterialPriceController
                                    .value = TextEditingValue(
                                  text: formattedValue,
                                  selection: TextSelection.collapsed(
                                    offset:
                                        cursorPos +
                                        (additionalChars > 0
                                            ? additionalChars
                                            : 0),
                                  ),
                                );
                              }
                            }
                          },
                        ),

                        const SizedBox(height: 12),

                        // Đơn vị đo lường
                        DropdownButtonFormField<model.MeasurementUnit>(
                          decoration: const InputDecoration(
                            labelText: 'Đơn vị tính',
                            border: OutlineInputBorder(),
                          ),
                          value: _selectedMeasurementUnit,
                          items:
                              model.MeasurementUnit.values.map((unit) {
                                return DropdownMenuItem<model.MeasurementUnit>(
                                  value: unit,
                                  child: Text(_getMeasurementUnitLabel(unit)),
                                );
                              }).toList(),
                          onChanged: (value) {
                            if (value != null) {
                              setState(() {
                                _selectedMeasurementUnit = value;
                                // Nếu chọn đơn vị tùy chỉnh, hiển thị trường nhập đơn vị
                                if (value == model.MeasurementUnit.custom) {
                                  _newMaterialUnitController.text = '';
                                } else {
                                  _newMaterialUnitController
                                      .text = _getUnitForMeasurementUnit(value);
                                }
                              });
                            }
                          },
                        ),

                        // Hiển thị trường nhập đơn vị tùy chỉnh nếu cần
                        if (_selectedMeasurementUnit ==
                            model.MeasurementUnit.custom)
                          Padding(
                            padding: const EdgeInsets.only(top: 12.0),
                            child: TextField(
                              controller: _newMaterialUnitController,
                              decoration: const InputDecoration(
                                labelText: 'Đơn vị tùy chỉnh',
                                border: OutlineInputBorder(),
                              ),
                            ),
                          ),

                        const SizedBox(height: 16),

                        // Nút thêm vật liệu
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed: () => _addNewMaterial(materialProvider),
                            child: const Padding(
                              padding: EdgeInsets.all(12.0),
                              child: Text(
                                'Thêm vật liệu',
                                style: TextStyle(fontSize: 16),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Nút lưu giá
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed:
                      _isSaving ? null : () => _savePrices(materialProvider),
                  child: Padding(
                    padding: const EdgeInsets.all(12.0),
                    child:
                        _isSaving
                            ? const CircularProgressIndicator()
                            : const Text(
                              'Lưu giá',
                              style: TextStyle(fontSize: 16),
                            ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// Chuyển đổi chuỗi đã định dạng thành số
  double? _parseFormattedNumber(String formattedValue) {
    // Loại bỏ tất cả dấu phẩy
    final cleanValue = formattedValue.replaceAll(',', '');
    return double.tryParse(cleanValue);
  }

  /// Lấy nhãn cho đơn vị đo lường
  String _getMeasurementUnitLabel(model.MeasurementUnit unit) {
    switch (unit) {
      case model.MeasurementUnit.piece:
        return 'Viên';
      case model.MeasurementUnit.cubicMeter:
        return 'Mét khối (m³)';
      case model.MeasurementUnit.squareMeter:
        return 'Mét vuông (m²)';
      case model.MeasurementUnit.meter:
        return 'Mét (m)';
      case model.MeasurementUnit.kilogram:
        return 'Kilogram (kg)';
      case model.MeasurementUnit.ton:
        return 'Tấn';
      case model.MeasurementUnit.set:
        return 'Bộ';
      case model.MeasurementUnit.package:
        return 'Gói';
      case model.MeasurementUnit.custom:
        return 'Tùy chỉnh';
    }
  }

  /// Lấy đơn vị cho đơn vị đo lường
  String _getUnitForMeasurementUnit(model.MeasurementUnit unit) {
    switch (unit) {
      case model.MeasurementUnit.piece:
        return 'viên';
      case model.MeasurementUnit.cubicMeter:
        return 'm³';
      case model.MeasurementUnit.squareMeter:
        return 'm²';
      case model.MeasurementUnit.meter:
        return 'm';
      case model.MeasurementUnit.kilogram:
        return 'kg';
      case model.MeasurementUnit.ton:
        return 'tấn';
      case model.MeasurementUnit.set:
        return 'bộ';
      case model.MeasurementUnit.package:
        return 'gói';
      case model.MeasurementUnit.custom:
        return '';
    }
  }

  /// Thêm vật liệu mới
  void _addNewMaterial(MaterialProvider materialProvider) {
    // Kiểm tra các trường bắt buộc
    if (_newMaterialNameController.text.isEmpty ||
        _newMaterialPriceController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Vui lòng nhập đầy đủ thông tin')),
      );
      return;
    }

    // Lấy giá trị
    final String name = _newMaterialNameController.text;
    final double price =
        _parseFormattedNumber(_newMaterialPriceController.text) ?? 0.0;

    // Lấy đơn vị
    String unit;
    if (_selectedMeasurementUnit == model.MeasurementUnit.custom) {
      if (_newMaterialUnitController.text.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Vui lòng nhập đơn vị tùy chỉnh')),
        );
        return;
      }
      unit = _newMaterialUnitController.text;
    } else {
      unit = _getUnitForMeasurementUnit(_selectedMeasurementUnit);
    }

    // Thêm vật liệu mới
    materialProvider.addCustomMaterial(
      name: name,
      pricePerUnit: price,
      customUnit: unit,
      measurementUnit: _selectedMeasurementUnit,
    );

    // Xóa các trường nhập liệu
    _newMaterialNameController.clear();
    _newMaterialPriceController.clear();
    _newMaterialUnitController.clear();

    // Thông báo thành công
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Đã thêm vật liệu mới')));
  }

  /// Lưu các giá đã được cập nhật
  Future<void> _savePrices(MaterialProvider materialProvider) async {
    setState(() {
      _isSaving = true;
    });

    try {
      // Cập nhật giá trong provider
      for (final material in materialProvider.materials) {
        final controller = _controllers[material.name];
        if (controller != null) {
          // Chuyển đổi giá trị đã định dạng thành số
          final newPrice =
              _parseFormattedNumber(controller.text) ?? material.pricePerUnit;
          materialProvider.updateMaterialPrice(material.name, newPrice);
        }
      }

      // Lưu vào shared preferences
      await _priceStorageService.saveAllPrices(materialProvider.materials);

      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('Đã lưu giá vật liệu')));
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Lỗi: ${e.toString()}')));
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }
}
