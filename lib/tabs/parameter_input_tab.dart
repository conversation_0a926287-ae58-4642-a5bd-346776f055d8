import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/material_provider.dart';
import '../models/material_model.dart' as material_model;
import '../utils/number_formatter.dart';

/// Tab cho việc nhập các thông số để tính toán
class ParameterInputTab extends StatefulWidget {
  const ParameterInputTab({super.key});

  @override
  State<ParameterInputTab> createState() => _ParameterInputTabState();
}

class _ParameterInputTabState extends State<ParameterInputTab> {
  // Các thông số được nhập vào
  final Map<String, dynamic> _parameters = {};
  final _formKey = GlobalKey<FormState>();
  double _quantity = 0; // Số lượng vật liệu
  double _cost = 0; // Chi phí
  bool _hasCalculated = false; // Đã tính toán hay chưa

  @override
  Widget build(BuildContext context) {
    return Consumer<MaterialProvider>(
      builder: (context, materialProvider, child) {
        final selectedMaterial = materialProvider.selectedMaterial;

        if (selectedMaterial == null) {
          return const Center(
            child: Text('<PERSON>ui lòng chọn vật liệu trước'),
          );
        }

        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Nhập thông số cho ${selectedMaterial.name}:',
                  style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),

                // Các trường nhập liệu động dựa trên vật liệu được chọn
                ..._buildParameterFields(selectedMaterial),

                const SizedBox(height: 24),

                // Nút tính toán
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      if (_formKey.currentState!.validate()) {
                        _calculateResults(selectedMaterial);
                      }
                    },
                    child: const Padding(
                      padding: EdgeInsets.all(12.0),
                      child: Text('Tính toán', style: TextStyle(fontSize: 16)),
                    ),
                  ),
                ),

                const SizedBox(height: 24),

                // Phần kết quả
                if (_hasCalculated) _buildResultsSection(selectedMaterial),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Xây dựng các trường nhập liệu dựa trên các thông số cần thiết của vật liệu được chọn
  List<Widget> _buildParameterFields(material_model.Material material) {
    final List<Widget> fields = [];

    for (final param in material.requiredParameters) {
      fields.add(
        Padding(
          padding: const EdgeInsets.only(bottom: 16.0),
          child: TextFormField(
            decoration: InputDecoration(
              labelText: _getParameterLabel(param),
              border: const OutlineInputBorder(),
              suffixText: _getParameterUnit(param),
            ),
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Vui lòng nhập giá trị';
              }
              if (double.tryParse(value) == null) {
                return 'Vui lòng nhập số hợp lệ';
              }
              return null;
            },
            onSaved: (value) {
              if (value != null) {
                // Đối với thông số số lượng, lưu dưới dạng int, các thông số khác lưu dưới dạng double
                if (param == 'quantity') {
                  _parameters[param] = int.tryParse(value) ?? 0;
                } else {
                  _parameters[param] = double.tryParse(value) ?? 0.0;
                }
              }
            },
          ),
        ),
      );
    }

    return fields;
  }

  /// Lấy nhãn thân thiện với người dùng cho một thông số
  String _getParameterLabel(String param) {
    switch (param) {
      case 'length':
        return 'Chiều dài';
      case 'width':
        return 'Chiều rộng';
      case 'height':
        return 'Chiều cao';
      case 'wallVolume':
        return 'Thể tích tường';
      case 'volume':
        return 'Thể tích';
      case 'area':
        return 'Diện tích';
      case 'surfaceArea':
        return 'Diện tích bề mặt';
      case 'quantity':
        return 'Số lượng';
      default:
        return param;
    }
  }

  /// Lấy đơn vị cho một thông số
  String _getParameterUnit(String param) {
    switch (param) {
      case 'length':
      case 'width':
      case 'height':
        return 'm';
      case 'wallVolume':
      case 'volume':
        return 'm³';
      case 'area':
      case 'surfaceArea':
        return 'm²';
      case 'quantity':
        return 'cái';
      default:
        return '';
    }
  }

  /// Tính toán kết quả dựa trên các thông số được nhập vào
  void _calculateResults(material_model.Material material) {
    _formKey.currentState!.save();

    setState(() {
      _quantity = material.calculateQuantity(_parameters);
      _cost = material.calculateCost(_parameters);
      _hasCalculated = true;
    });
  }

  /// Xây dựng phần hiển thị kết quả
  Widget _buildResultsSection(material_model.Material material) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Kết quả tính toán:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(
              'Khối lượng: ${NumberFormatter.format(_quantity)} ${material.unit}',
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 4),
            Text(
              'Chi phí: ${NumberFormatter.formatCurrency(_cost)} VNĐ',
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ],
        ),
      ),
    );
  }
}
