import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Extension cho ThemeData để hỗ trợ SoftGradientBackground
class GradientTheme extends ThemeExtension<GradientTheme> {
  final List<Color> primaryGradientColors;
  final List<Color> secondaryGradientColors;

  GradientTheme({
    required this.primaryGradientColors,
    required this.secondaryGradientColors,
  });

  @override
  ThemeExtension<GradientTheme> copyWith({
    List<Color>? primaryGradientColors,
    List<Color>? secondaryGradientColors,
  }) {
    return GradientTheme(
      primaryGradientColors:
          primaryGradientColors ?? this.primaryGradientColors,
      secondaryGradientColors:
          secondaryGradientColors ?? this.secondaryGradientColors,
    );
  }

  @override
  ThemeExtension<GradientTheme> lerp(
    ThemeExtension<GradientTheme>? other,
    double t,
  ) {
    if (other is! GradientTheme) {
      return this;
    }
    return GradientTheme(
      primaryGradientColors: primaryGradientColors,
      secondaryGradientColors: secondaryGradientColors,
    );
  }

  /// Helper method để lấy GradientTheme từ context
  static GradientTheme of(BuildContext context) {
    return Theme.of(context).extension<GradientTheme>() ??
        GradientTheme(
          primaryGradientColors: [
            Colors.purple.shade200,
            Colors.pink.shade200,
            Colors.orange.shade200,
            Colors.amber.shade100,
          ],
          secondaryGradientColors: [
            Colors.blue.shade200,
            Colors.cyan.shade200,
            Colors.teal.shade200,
            Colors.green.shade100,
          ],
        );
  }
}

/// Theme chính cho ứng dụng dự toán xây dựng
class ConstructionTheme {
  // Màu sắc chính - Theme sáng
  static const Color primaryColor = Color(0xFF6B8BC9); // Xanh dương chủ đạo
  static const Color secondaryColor = Color(0xFFFFB27D); // Cam nhẹ
  static const Color accentColor = Color(0xFFA9D4D9); // Xanh lam nhạt
  static const Color backgroundColor = Color(0xFFF5F5FA); // Xám nhạt làm nền

  // Màu thẻ - Theme sáng
  static const Color cardGreenColor = Color(0xFFB8E2D0); // Xanh lá nhạt cho thẻ
  static const Color cardBlueColor = Color(
    0xFFB8E2EC,
  ); // Xanh dương nhạt cho thẻ
  static const Color cardPurpleColor = Color(0xFFD4C7E7); // Tím nhạt cho thẻ
  static const Color cardYellowColor = Color(0xFFFFF4D8); // Vàng nhạt cho thẻ

  // Màu văn bản - Theme sáng
  static const Color textPrimaryColor = Color(0xFF2D3142); // Màu chữ chính
  static const Color textSecondaryColor = Color(0xFF4F5D75); // Màu chữ phụ
  static const Color textLightColor = Color(0xFF8D99AE); // Màu chữ nhạt

  // Màu sắc chính - Theme tối
  static const Color darkPrimaryColor = Color(0xFF5A75A6); // Xanh dương đậm hơn
  static const Color darkSecondaryColor = Color(0xFFE59B69); // Cam đậm hơn
  static const Color darkAccentColor = Color(0xFF8AACB0); // Xanh lam đậm hơn
  static const Color darkBackgroundColor = Color(0xFF1E1E2C); // Xám đen làm nền

  // Màu thẻ - Theme tối
  static const Color darkCardGreenColor = Color(
    0xFF2A6B4F,
  ); // Xanh lá đậm cho thẻ
  static const Color darkCardBlueColor = Color(
    0xFF2A6B7B,
  ); // Xanh dương đậm cho thẻ
  static const Color darkCardPurpleColor = Color(0xFF5D4D7A); // Tím đậm cho thẻ
  static const Color darkCardYellowColor = Color(
    0xFF7A6940,
  ); // Vàng đậm cho thẻ

  // Màu văn bản - Theme tối
  static const Color darkTextPrimaryColor = Color(0xFFE0E0E0); // Màu chữ chính
  static const Color darkTextSecondaryColor = Color(0xFFB0B0B0); // Màu chữ phụ
  static const Color darkTextLightColor = Color(0xFF8D8D8D); // Màu chữ nhạt

  // Màu gradient - Theme sáng
  static const List<Color> primaryGradient = [
    Color(0xFF6B8BC9),
    Color(0xFF8FA8D9),
  ];
  static const List<Color> secondaryGradient = [
    Color(0xFFFFB27D),
    Color(0xFFFFD0A8),
  ];
  static const List<Color> accentGradient = [
    Color(0xFFA9D4D9),
    Color(0xFFC7E4E7),
  ];

  // Màu gradient - Theme tối
  static const List<Color> darkPrimaryGradient = [
    Color(0xFF5A75A6),
    Color(0xFF3D5280),
  ];
  static const List<Color> darkSecondaryGradient = [
    Color(0xFFE59B69),
    Color(0xFFC27A4A),
  ];
  static const List<Color> darkAccentGradient = [
    Color(0xFF8AACB0),
    Color(0xFF6A8A8E),
  ];

  // Theme sáng
  static final ThemeData lightTheme = ThemeData(
    primaryColor: primaryColor,
    scaffoldBackgroundColor: backgroundColor,
    fontFamily: 'Poppins',
    brightness: Brightness.light,

    // AppBar theme
    appBarTheme: const AppBarTheme(
      backgroundColor: primaryColor,
      elevation: 0,
      centerTitle: true,
      iconTheme: IconThemeData(color: Colors.white),
      titleTextStyle: TextStyle(
        fontFamily: 'Poppins',
        fontWeight: FontWeight.w600,
        fontSize: 18,
        color: Colors.white,
      ),
      systemOverlayStyle: SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
      ),
    ),

    // Text theme
    textTheme: const TextTheme(
      displayLarge: TextStyle(
        fontSize: 28,
        fontWeight: FontWeight.bold,
        color: textPrimaryColor,
      ),
      displayMedium: TextStyle(
        fontSize: 24,
        fontWeight: FontWeight.bold,
        color: textPrimaryColor,
      ),
      displaySmall: TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.bold,
        color: textPrimaryColor,
      ),
      headlineMedium: TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.w600,
        color: textPrimaryColor,
      ),
      titleLarge: TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w600,
        color: textPrimaryColor,
      ),
      bodyLarge: TextStyle(fontSize: 16, color: textSecondaryColor),
      bodyMedium: TextStyle(fontSize: 14, color: textSecondaryColor),
      labelLarge: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w600,
        color: primaryColor,
      ),
    ),

    // Card theme
    cardTheme: CardTheme(
      color: Colors.white,
      elevation: 2,
      shadowColor: Colors.black.withAlpha(26),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 0),
    ),

    // Button themes
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: primaryColor,
        foregroundColor: Colors.white,
        elevation: 2,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        textStyle: const TextStyle(
          fontFamily: 'Poppins',
          fontWeight: FontWeight.w600,
          fontSize: 14,
        ),
      ),
    ),

    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: primaryColor,
        side: const BorderSide(color: primaryColor, width: 1.5),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        textStyle: const TextStyle(
          fontFamily: 'Poppins',
          fontWeight: FontWeight.w600,
          fontSize: 14,
        ),
      ),
    ),

    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: primaryColor,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        textStyle: const TextStyle(
          fontFamily: 'Poppins',
          fontWeight: FontWeight.w600,
          fontSize: 14,
        ),
      ),
    ),

    // Input decoration theme
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: Colors.white,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: Color(0xFFE0E0E0), width: 1),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: Color(0xFFE0E0E0), width: 1),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: primaryColor, width: 1.5),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: Colors.red, width: 1),
      ),
      labelStyle: const TextStyle(
        fontFamily: 'Poppins',
        fontSize: 14,
        color: textSecondaryColor,
      ),
      hintStyle: const TextStyle(
        fontFamily: 'Poppins',
        fontSize: 14,
        color: textLightColor,
      ),
    ),

    // Bottom navigation bar theme
    bottomNavigationBarTheme: const BottomNavigationBarThemeData(
      backgroundColor: Colors.white,
      selectedItemColor: primaryColor,
      unselectedItemColor: textLightColor,
      selectedLabelStyle: TextStyle(
        fontFamily: 'Poppins',
        fontSize: 12,
        fontWeight: FontWeight.w500,
      ),
      unselectedLabelStyle: TextStyle(fontFamily: 'Poppins', fontSize: 12),
      type: BottomNavigationBarType.fixed,
      elevation: 8,
    ),

    // Color scheme
    colorScheme: const ColorScheme.light(
      primary: primaryColor,
      secondary: secondaryColor,
      surface: Colors.white,
      error: Colors.red,
      onPrimary: Colors.white,
      onSecondary: textPrimaryColor,
      onSurface: textPrimaryColor,
      onError: Colors.white,
      brightness: Brightness.light,
    ),

    // Gradient theme extension
    extensions: [
      GradientTheme(
        primaryGradientColors: [
          Colors.purple.shade200,
          Colors.pink.shade200,
          Colors.orange.shade200,
          Colors.amber.shade100,
        ],
        secondaryGradientColors: [
          Colors.blue.shade200,
          Colors.cyan.shade200,
          Colors.teal.shade200,
          Colors.green.shade100,
        ],
      ),
    ],
  );

  // Theme tối
  static final ThemeData darkTheme = ThemeData(
    primaryColor: darkPrimaryColor,
    scaffoldBackgroundColor: darkBackgroundColor,
    fontFamily: 'Poppins',
    brightness: Brightness.dark,

    // AppBar theme
    appBarTheme: const AppBarTheme(
      backgroundColor: darkPrimaryColor,
      elevation: 0,
      centerTitle: true,
      iconTheme: IconThemeData(color: Colors.white),
      titleTextStyle: TextStyle(
        fontFamily: 'Poppins',
        fontWeight: FontWeight.w600,
        fontSize: 18,
        color: Colors.white,
      ),
      systemOverlayStyle: SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
      ),
    ),

    // Text theme
    textTheme: const TextTheme(
      displayLarge: TextStyle(
        fontSize: 28,
        fontWeight: FontWeight.bold,
        color: darkTextPrimaryColor,
      ),
      displayMedium: TextStyle(
        fontSize: 24,
        fontWeight: FontWeight.bold,
        color: darkTextPrimaryColor,
      ),
      displaySmall: TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.bold,
        color: darkTextPrimaryColor,
      ),
      headlineMedium: TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.w600,
        color: darkTextPrimaryColor,
      ),
      titleLarge: TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w600,
        color: darkTextPrimaryColor,
      ),
      bodyLarge: TextStyle(fontSize: 16, color: darkTextSecondaryColor),
      bodyMedium: TextStyle(fontSize: 14, color: darkTextSecondaryColor),
      labelLarge: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w600,
        color: darkPrimaryColor,
      ),
    ),

    // Card theme
    cardTheme: CardTheme(
      color: Color(0xFF2A2A3A), // Màu nền card tối
      elevation: 2,
      shadowColor: Colors.black.withAlpha(50),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 0),
    ),

    // Button themes
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: darkPrimaryColor,
        foregroundColor: Colors.white,
        elevation: 2,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        textStyle: const TextStyle(
          fontFamily: 'Poppins',
          fontWeight: FontWeight.w600,
          fontSize: 14,
        ),
      ),
    ),

    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: darkPrimaryColor,
        side: const BorderSide(color: darkPrimaryColor, width: 1.5),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        textStyle: const TextStyle(
          fontFamily: 'Poppins',
          fontWeight: FontWeight.w600,
          fontSize: 14,
        ),
      ),
    ),

    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: darkPrimaryColor,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        textStyle: const TextStyle(
          fontFamily: 'Poppins',
          fontWeight: FontWeight.w600,
          fontSize: 14,
        ),
      ),
    ),

    // Input decoration theme
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: Color(0xFF2A2A3A), // Màu nền input tối
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: Color(0xFF3A3A4A), width: 1),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: Color(0xFF3A3A4A), width: 1),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: darkPrimaryColor, width: 1.5),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: Colors.red, width: 1),
      ),
      labelStyle: const TextStyle(
        fontFamily: 'Poppins',
        fontSize: 14,
        color: darkTextSecondaryColor,
      ),
      hintStyle: const TextStyle(
        fontFamily: 'Poppins',
        fontSize: 14,
        color: darkTextLightColor,
      ),
    ),

    // Bottom navigation bar theme
    bottomNavigationBarTheme: const BottomNavigationBarThemeData(
      backgroundColor: Color(0xFF2A2A3A), // Màu nền bottom navigation bar tối
      selectedItemColor: darkPrimaryColor,
      unselectedItemColor: darkTextLightColor,
      selectedLabelStyle: TextStyle(
        fontFamily: 'Poppins',
        fontSize: 12,
        fontWeight: FontWeight.w500,
      ),
      unselectedLabelStyle: TextStyle(fontFamily: 'Poppins', fontSize: 12),
      type: BottomNavigationBarType.fixed,
      elevation: 8,
    ),

    // Color scheme
    colorScheme: const ColorScheme.dark(
      primary: darkPrimaryColor,
      secondary: darkSecondaryColor,
      surface: Color(0xFF2A2A3A), // Màu nền surface tối
      error: Colors.red,
      onPrimary: Colors.white,
      onSecondary: darkTextPrimaryColor,
      onSurface: darkTextPrimaryColor,
      onError: Colors.white,
      brightness: Brightness.dark,
    ),

    // Gradient theme extension
    extensions: [
      GradientTheme(
        primaryGradientColors: [
          Colors.purple.shade700,
          Colors.pink.shade700,
          Colors.orange.shade700,
          Colors.amber.shade600,
        ],
        secondaryGradientColors: [
          Colors.blue.shade700,
          Colors.cyan.shade700,
          Colors.teal.shade700,
          Colors.green.shade600,
        ],
      ),
    ],
  );
}

/// Widget tùy chỉnh phù hợp với theme
class CustomWidgets {
  /// Card với nền gradient hiển thị các chỉ số quan trọng
  static Widget gradientCard({
    required String title,
    required String value,
    required List<Color> gradientColors,
    Icon? icon,
    String? subtitle,
    double? width,
  }) {
    return Container(
      width: width,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: gradientColors,
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: gradientColors[0].withAlpha(77),
            blurRadius: 8,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontFamily: 'Poppins',
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: ConstructionTheme.textPrimaryColor,
                ),
              ),
              if (icon != null) icon,
            ],
          ),
          const SizedBox(height: 8),
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                value,
                style: const TextStyle(
                  fontFamily: 'Poppins',
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: ConstructionTheme.textPrimaryColor,
                ),
              ),
              if (subtitle != null)
                Padding(
                  padding: const EdgeInsets.only(left: 4, bottom: 4),
                  child: Text(
                    subtitle,
                    style: const TextStyle(
                      fontFamily: 'Poppins',
                      fontSize: 12,
                      color: ConstructionTheme.textSecondaryColor,
                    ),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  /// Thẻ hiển thị các chỉ số với nền đơn sắc
  static Widget metricCard({
    required String title,
    required String value,
    required Color backgroundColor,
    IconData? icon,
    String? subtitle,
    double? width,
  }) {
    return Container(
      width: width,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: backgroundColor.withAlpha(128),
            blurRadius: 8,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              if (icon != null)
                Icon(
                  icon,
                  size: 18,
                  color: ConstructionTheme.textSecondaryColor,
                ),
              if (icon != null) const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: const TextStyle(
                    fontFamily: 'Poppins',
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: ConstructionTheme.textSecondaryColor,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                value,
                style: const TextStyle(
                  fontFamily: 'Poppins',
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                  color: ConstructionTheme.textPrimaryColor,
                ),
              ),
              if (subtitle != null)
                Padding(
                  padding: const EdgeInsets.only(left: 4, bottom: 3),
                  child: Text(
                    subtitle,
                    style: const TextStyle(
                      fontFamily: 'Poppins',
                      fontSize: 12,
                      color: ConstructionTheme.textSecondaryColor,
                    ),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  /// Nút tròn với icon
  static Widget circleButton({
    required IconData icon,
    required VoidCallback onPressed,
    Color? backgroundColor,
    Color? iconColor,
    double size = 56,
  }) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: backgroundColor ?? ConstructionTheme.primaryColor,
        boxShadow: [
          BoxShadow(
            color: (backgroundColor ?? ConstructionTheme.primaryColor)
                .withAlpha(77),
            blurRadius: 8,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: IconButton(
        icon: Icon(icon, color: iconColor ?? Colors.white, size: size * 0.5),
        onPressed: onPressed,
        padding: EdgeInsets.zero,
      ),
    );
  }

  /// Thẻ hiển thị thông tin chi phí với khả năng hiển thị thay đổi
  static Widget costInfoCard({
    required String title,
    required String amount,
    required String unit,
    String? change,
    Color? backgroundColor,
    double? width,
  }) {
    final bgColor = backgroundColor ?? ConstructionTheme.cardGreenColor;
    final isPositiveChange = change != null && change.contains('+');

    return Container(
      width: width,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: bgColor.withAlpha(128),
            blurRadius: 8,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontFamily: 'Poppins',
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: ConstructionTheme.textSecondaryColor,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                amount,
                style: const TextStyle(
                  fontFamily: 'Poppins',
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: ConstructionTheme.textPrimaryColor,
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(left: 4, bottom: 4),
                child: Text(
                  unit,
                  style: const TextStyle(
                    fontFamily: 'Poppins',
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: ConstructionTheme.textSecondaryColor,
                  ),
                ),
              ),
            ],
          ),
          if (change != null)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Row(
                children: [
                  Icon(
                    isPositiveChange
                        ? Icons.arrow_upward
                        : Icons.arrow_downward,
                    size: 14,
                    color: isPositiveChange ? Colors.green : Colors.red,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    change,
                    style: TextStyle(
                      fontFamily: 'Poppins',
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: isPositiveChange ? Colors.green : Colors.red,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }
}
