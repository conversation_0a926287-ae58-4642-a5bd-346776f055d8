import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Provider quản lý theme của ứng dụng
class ThemeProvider extends ChangeNotifier {
  /// Key lưu trữ theme trong SharedPreferences
  static const String _themePreferenceKey = 'theme_preference';

  /// Theme mode hiện tại
  ThemeMode _themeMode = ThemeMode.system;

  /// Getter cho theme mode hiện tại
  ThemeMode get themeMode => _themeMode;

  /// Constructor
  ThemeProvider() {
    _loadThemePreference();
  }

  /// Tải theme preference từ SharedPreferences
  Future<void> _loadThemePreference() async {
    final prefs = await SharedPreferences.getInstance();
    final themeString = prefs.getString(_themePreferenceKey);
    
    if (themeString != null) {
      if (themeString == 'light') {
        _themeMode = ThemeMode.light;
      } else if (themeString == 'dark') {
        _themeMode = ThemeMode.dark;
      } else {
        _themeMode = ThemeMode.system;
      }
      notifyListeners();
    }
  }

  /// <PERSON>ưu theme preference vào SharedPreferences
  Future<void> _saveThemePreference(ThemeMode mode) async {
    final prefs = await SharedPreferences.getInstance();
    String themeString;
    
    if (mode == ThemeMode.light) {
      themeString = 'light';
    } else if (mode == ThemeMode.dark) {
      themeString = 'dark';
    } else {
      themeString = 'system';
    }
    
    await prefs.setString(_themePreferenceKey, themeString);
  }

  /// Chuyển đổi theme
  Future<void> toggleTheme() async {
    if (_themeMode == ThemeMode.light) {
      _themeMode = ThemeMode.dark;
    } else if (_themeMode == ThemeMode.dark) {
      _themeMode = ThemeMode.system;
    } else {
      _themeMode = ThemeMode.light;
    }
    
    await _saveThemePreference(_themeMode);
    notifyListeners();
  }

  /// Đặt theme cụ thể
  Future<void> setTheme(ThemeMode mode) async {
    _themeMode = mode;
    await _saveThemePreference(mode);
    notifyListeners();
  }

  /// Kiểm tra xem theme hiện tại có phải là theme tối không
  bool isDarkMode(BuildContext context) {
    if (_themeMode == ThemeMode.system) {
      return MediaQuery.of(context).platformBrightness == Brightness.dark;
    }
    return _themeMode == ThemeMode.dark;
  }
}
