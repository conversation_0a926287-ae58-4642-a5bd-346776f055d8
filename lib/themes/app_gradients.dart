import 'package:flutter/material.dart';
import 'app_colors.dart';

/// Đ<PERSON>nh nghĩa các gradient cho ứng dụng theo thiết kế request11.md
class AppGradients {
  // Card Gradient (Pink to Purple - theo ảnh thẻ)
  static const LinearGradient cardGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      AppColors.cardPinkStart,    // #FF9EC7
      AppColors.cardPurpleEnd,    // #9C7FFF
    ],
  );
  
  // Background Gradient (Purple to Dark - theo ảnh background)  
  static const LinearGradient backgroundGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      AppColors.bgPurpleTop,      // #7758CE
      AppColors.bgBlueMiddle1,    // #172679
      AppColors.bgBlueMiddle2,    // #080E46
      AppColors.bgDarkBottom,     // #00000B
    ],
    stops: [0.0, 0.3, 0.65, 1.0],
  );
  
  // Alternative gradients cho customization
  static const LinearGradient cardGradientReverse = LinearGradient(
    begin: Alignment.bottomRight,
    end: Alignment.topLeft,
    colors: [
      AppColors.cardPinkStart,
      AppColors.cardPurpleEnd,
    ],
  );
  
  // Horizontal card gradient
  static const LinearGradient cardGradientHorizontal = LinearGradient(
    begin: Alignment.centerLeft,
    end: Alignment.centerRight,
    colors: [
      AppColors.cardPinkStart,
      AppColors.cardPurpleEnd,
    ],
  );
  
  // Subtle overlay gradient
  static LinearGradient overlayGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      AppColors.surfaceOverlay,
      AppColors.surfaceOverlay.withValues(alpha: 0.1),
    ],
  );
  
  // Button gradient
  static const LinearGradient buttonGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      AppColors.cardPinkStart,
      AppColors.cardPurpleEnd,
    ],
  );
}
