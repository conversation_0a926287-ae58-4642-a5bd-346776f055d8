import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../models/project/project_provider.dart';
import '../../../models/project/building_model.dart';
import '../../../models/material_provider.dart';
import '../../../services/material_calculator.dart';
import '../../../widgets/gradient_button.dart';

/// Bước 5: Nhập thông số chi tiết
class Step5DetailedParameters extends StatefulWidget {
  /// Callback khi nhấn nút hoàn thành
  final VoidCallback onComplete;

  /// Callback khi nhấn nút quay lại
  final VoidCallback onBack;

  const Step5DetailedParameters({
    super.key,
    required this.onComplete,
    required this.onBack,
  });

  @override
  State<Step5DetailedParameters> createState() =>
      _Step5DetailedParametersState();
}

class _Step5DetailedParametersState extends State<Step5DetailedParameters> {
  // Thông số móng - <PERSON>h sách động các controller
  final List<TextEditingController> _foundationLengthControllers = [];
  final TextEditingController _foundationColumnsController =
      TextEditingController();

  // Thông số tường - Danh sách động các thông số tường
  final List<Map<String, dynamic>> _wallParams = [];

  // Thông số cửa - Danh sách động các thông số cửa
  final List<Map<String, dynamic>> _doorParams = [];
  final List<Map<String, dynamic>> _windowParams = [];
  final List<Map<String, dynamic>> _rollingDoorParams = [];

  // Thông số khác
  final List<TextEditingController> _bathroomAreaControllers = [];
  final TextEditingController _gypsumCeilingAreaController =
      TextEditingController();
  final List<TextEditingController> _stairsStepsControllers = [];

  // Thông số chi tiết từ dự án nháp
  DetailedParameters? _detailedParameters;

  @override
  void initState() {
    super.initState();

    // Lấy dữ liệu từ dự án nháp nếu có
    final draftProject =
        Provider.of<ProjectProvider>(context, listen: false).draftProject;
    if (draftProject != null) {
      _detailedParameters = draftProject.detailedParameters;

      // Điền dữ liệu vào các controller
      // Xử lý thông số móng
      if (_detailedParameters != null) {
        if (_detailedParameters!.foundation.containsKey('lengths')) {
          // Nếu có danh sách chiều dài móng
          final List<dynamic>? foundationLengths =
              _detailedParameters!.foundation['lengths'] as List<dynamic>?;
          if (foundationLengths != null && foundationLengths.isNotEmpty) {
            for (final length in foundationLengths) {
              final controller = TextEditingController(text: length.toString());
              _foundationLengthControllers.add(controller);
            }
          }
        } else if (_detailedParameters!.foundation.containsKey('length')) {
          // Nếu chỉ có một chiều dài móng (tương thích với dữ liệu cũ)
          final length = _detailedParameters!.foundation['length'];
          if (length != null) {
            final controller = TextEditingController(text: length.toString());
            _foundationLengthControllers.add(controller);
          }
        }
      }

      // Nếu không có dữ liệu móng, thêm một controller mặc định
      if (_foundationLengthControllers.isEmpty) {
        _foundationLengthControllers.add(TextEditingController(text: '0'));
      }

      _foundationColumnsController.text =
          _detailedParameters?.foundation['columns']?.toString() ?? '0';

      // Xử lý thông số tường
      if (_detailedParameters != null) {
        if (_detailedParameters!.walls.containsKey('walls')) {
          // Nếu có danh sách tường
          final List<dynamic>? walls =
              _detailedParameters!.walls['walls'] as List<dynamic>?;
          if (walls != null && walls.isNotEmpty) {
            for (final wall in walls) {
              _wallParams.add({
                'type': wall['type'] ?? '10',
                'plasterSides': wall['plasterSides'] ?? 2,
                'length': wall['length'] ?? 0.0,
                'height': wall['height'] ?? 0.0,
                'lengthController': TextEditingController(
                  text: (wall['length'] ?? 0.0).toString(),
                ),
                'heightController': TextEditingController(
                  text: (wall['height'] ?? 0.0).toString(),
                ),
              });
            }
          }
        } else if (_detailedParameters!.walls.containsKey('area')) {
          // Nếu chỉ có diện tích tường (tương thích với dữ liệu cũ)
          final area = _detailedParameters!.walls['area'];
          if (area != null && area > 0) {
            // Giả định tường có chiều cao 3m và chiều dài = diện tích / 3
            final length = area / 3.0;
            _wallParams.add({
              'type': '10',
              'plasterSides': 2,
              'length': length,
              'height': 3.0,
              'lengthController': TextEditingController(
                text: length.toString(),
              ),
              'heightController': TextEditingController(text: '3.0'),
            });
          }
        }
      }

      // Nếu không có dữ liệu tường, thêm một tường mặc định
      if (_wallParams.isEmpty) {
        _wallParams.add({
          'type': '10',
          'plasterSides': 2,
          'length': 0.0,
          'height': 0.0,
          'lengthController': TextEditingController(text: '0'),
          'heightController': TextEditingController(text: '0'),
        });
      }

      // Xử lý thông số cửa
      if (_detailedParameters != null &&
          _detailedParameters!.doors.isNotEmpty) {
        // Xử lý cửa sổ
        if (_detailedParameters!.doors.containsKey('windows')) {
          final List<dynamic>? windows =
              _detailedParameters!.doors['windows'] as List<dynamic>?;
          if (windows != null && windows.isNotEmpty) {
            for (final window in windows) {
              _windowParams.add({
                'width': window['width'] ?? 0.0,
                'height': window['height'] ?? 0.0,
                'quantity': window['quantity'] ?? 1,
                'widthController': TextEditingController(
                  text: (window['width'] ?? 0.0).toString(),
                ),
                'heightController': TextEditingController(
                  text: (window['height'] ?? 0.0).toString(),
                ),
                'quantityController': TextEditingController(
                  text: (window['quantity'] ?? 1).toString(),
                ),
              });
            }
          }
        } else if (_detailedParameters!.doors.containsKey('windowArea')) {
          // Tương thích với dữ liệu cũ
          final windowArea =
              _detailedParameters!.doors['windowArea'] as double?;
          if (windowArea != null && windowArea > 0) {
            // Giả định cửa sổ có kích thước 1m x 1m
            _windowParams.add({
              'width': 1.0,
              'height': 1.0,
              'quantity': windowArea.round(),
              'widthController': TextEditingController(text: '1.0'),
              'heightController': TextEditingController(text: '1.0'),
              'quantityController': TextEditingController(
                text: windowArea.round().toString(),
              ),
            });
          }
        }

        // Xử lý cửa đi
        if (_detailedParameters!.doors.containsKey('doors')) {
          final List<dynamic>? doors =
              _detailedParameters!.doors['doors'] as List<dynamic>?;
          if (doors != null && doors.isNotEmpty) {
            for (final door in doors) {
              _doorParams.add({
                'width': door['width'] ?? 0.0,
                'height': door['height'] ?? 0.0,
                'quantity': door['quantity'] ?? 1,
                'widthController': TextEditingController(
                  text: (door['width'] ?? 0.0).toString(),
                ),
                'heightController': TextEditingController(
                  text: (door['height'] ?? 0.0).toString(),
                ),
                'quantityController': TextEditingController(
                  text: (door['quantity'] ?? 1).toString(),
                ),
              });
            }
          }
        } else if (_detailedParameters!.doors.containsKey('doorArea')) {
          // Tương thích với dữ liệu cũ
          final doorArea = _detailedParameters!.doors['doorArea'] as double?;
          if (doorArea != null && doorArea > 0) {
            // Giả định cửa đi có kích thước 0.9m x 2.2m
            _doorParams.add({
              'width': 0.9,
              'height': 2.2,
              'quantity': (doorArea / (0.9 * 2.2)).round(),
              'widthController': TextEditingController(text: '0.9'),
              'heightController': TextEditingController(text: '2.2'),
              'quantityController': TextEditingController(
                text: (doorArea / (0.9 * 2.2)).round().toString(),
              ),
            });
          }
        }

        // Xử lý cửa cuốn
        if (_detailedParameters!.doors.containsKey('rollingDoors')) {
          final List<dynamic>? rollingDoors =
              _detailedParameters!.doors['rollingDoors'] as List<dynamic>?;
          if (rollingDoors != null && rollingDoors.isNotEmpty) {
            for (final rollingDoor in rollingDoors) {
              _rollingDoorParams.add({
                'width': rollingDoor['width'] ?? 0.0,
                'height': rollingDoor['height'] ?? 0.0,
                'quantity': rollingDoor['quantity'] ?? 1,
                'widthController': TextEditingController(
                  text: (rollingDoor['width'] ?? 0.0).toString(),
                ),
                'heightController': TextEditingController(
                  text: (rollingDoor['height'] ?? 0.0).toString(),
                ),
                'quantityController': TextEditingController(
                  text: (rollingDoor['quantity'] ?? 1).toString(),
                ),
              });
            }
          }
        } else if (_detailedParameters!.doors.containsKey('rollingDoorArea')) {
          // Tương thích với dữ liệu cũ
          final rollingDoorArea =
              _detailedParameters!.doors['rollingDoorArea'] as double?;
          if (rollingDoorArea != null && rollingDoorArea > 0) {
            // Giả định cửa cuốn có kích thước 3m x 2.5m
            _rollingDoorParams.add({
              'width': 3.0,
              'height': 2.5,
              'quantity': (rollingDoorArea / (3.0 * 2.5)).round(),
              'widthController': TextEditingController(text: '3.0'),
              'heightController': TextEditingController(text: '2.5'),
              'quantityController': TextEditingController(
                text: (rollingDoorArea / (3.0 * 2.5)).round().toString(),
              ),
            });
          }
        }
      }

      // Nếu không có dữ liệu cửa, thêm các cửa mặc định
      if (_windowParams.isEmpty) {
        _windowParams.add({
          'width': 0.0,
          'height': 0.0,
          'quantity': 0,
          'widthController': TextEditingController(text: '0'),
          'heightController': TextEditingController(text: '0'),
          'quantityController': TextEditingController(text: '0'),
        });
      }

      if (_doorParams.isEmpty) {
        _doorParams.add({
          'width': 0.0,
          'height': 0.0,
          'quantity': 0,
          'widthController': TextEditingController(text: '0'),
          'heightController': TextEditingController(text: '0'),
          'quantityController': TextEditingController(text: '0'),
        });
      }

      if (_rollingDoorParams.isEmpty) {
        _rollingDoorParams.add({
          'width': 0.0,
          'height': 0.0,
          'quantity': 0,
          'widthController': TextEditingController(text: '0'),
          'heightController': TextEditingController(text: '0'),
          'quantityController': TextEditingController(text: '0'),
        });
      }

      // Xử lý thông số nhà vệ sinh
      if (_detailedParameters != null &&
          _detailedParameters!.others.isNotEmpty) {
        if (_detailedParameters!.others.containsKey('bathrooms')) {
          final List<dynamic>? bathrooms =
              _detailedParameters!.others['bathrooms'] as List<dynamic>?;
          if (bathrooms != null && bathrooms.isNotEmpty) {
            for (final bathroom in bathrooms) {
              final area = bathroom['area'] as double?;
              if (area != null) {
                _bathroomAreaControllers.add(
                  TextEditingController(text: area.toString()),
                );
              }
            }
          }
        } else if (_detailedParameters!.others.containsKey('bathroomCount')) {
          // Tương thích với dữ liệu cũ
          final bathroomCount =
              _detailedParameters!.others['bathroomCount'] as int?;
          if (bathroomCount != null && bathroomCount > 0) {
            // Giả định mỗi phòng vệ sinh có diện tích 4m²
            for (int i = 0; i < bathroomCount; i++) {
              _bathroomAreaControllers.add(TextEditingController(text: '4.0'));
            }
          }
        }

        // Xử lý thông số thạch cao
        _gypsumCeilingAreaController.text =
            _detailedParameters?.others['gypsumCeilingArea']?.toString() ?? '0';

        // Xử lý thông số cầu thang
        if (_detailedParameters!.others.containsKey('stairs')) {
          final List<dynamic>? stairs =
              _detailedParameters!.others['stairs'] as List<dynamic>?;
          if (stairs != null && stairs.isNotEmpty) {
            for (final stair in stairs) {
              final steps = stair['steps'] as int?;
              if (steps != null) {
                _stairsStepsControllers.add(
                  TextEditingController(text: steps.toString()),
                );
              }
            }
          }
        } else if (_detailedParameters!.others.containsKey('stairsSteps')) {
          // Tương thích với dữ liệu cũ
          final stairsSteps =
              _detailedParameters!.others['stairsSteps'] as int?;
          if (stairsSteps != null && stairsSteps > 0) {
            _stairsStepsControllers.add(
              TextEditingController(text: stairsSteps.toString()),
            );
          }
        }
      }

      // Nếu không có dữ liệu nhà vệ sinh, thêm một controller mặc định
      if (_bathroomAreaControllers.isEmpty) {
        _bathroomAreaControllers.add(TextEditingController(text: '0'));
      }

      // Nếu không có dữ liệu cầu thang, thêm một controller mặc định
      if (_stairsStepsControllers.isEmpty) {
        _stairsStepsControllers.add(TextEditingController(text: '0'));
      }
    } else {
      // Nếu không có dữ liệu, thêm một controller mặc định cho móng
      _foundationLengthControllers.add(TextEditingController(text: '0'));

      // Đặt giá trị mặc định cho các controller khác
      _foundationColumnsController.text = '0';

      // Thêm một tường mặc định
      _wallParams.add({
        'type': '10',
        'plasterSides': 2,
        'length': 0.0,
        'height': 0.0,
        'lengthController': TextEditingController(text: '0'),
        'heightController': TextEditingController(text: '0'),
      });

      // Thêm các cửa mặc định
      _windowParams.add({
        'width': 0.0,
        'height': 0.0,
        'quantity': 1,
        'widthController': TextEditingController(text: '0'),
        'heightController': TextEditingController(text: '0'),
        'quantityController': TextEditingController(text: '0'),
      });

      _doorParams.add({
        'width': 0.0,
        'height': 0.0,
        'quantity': 1,
        'widthController': TextEditingController(text: '0'),
        'heightController': TextEditingController(text: '0'),
        'quantityController': TextEditingController(text: '0'),
      });

      _rollingDoorParams.add({
        'width': 0.0,
        'height': 0.0,
        'quantity': 1,
        'widthController': TextEditingController(text: '0'),
        'heightController': TextEditingController(text: '0'),
        'quantityController': TextEditingController(text: '0'),
      });

      // Thêm controller mặc định cho nhà vệ sinh
      _bathroomAreaControllers.add(TextEditingController(text: '0'));

      // Đặt giá trị mặc định cho thạch cao
      _gypsumCeilingAreaController.text = '0';

      // Thêm controller mặc định cho cầu thang
      _stairsStepsControllers.add(TextEditingController(text: '0'));
    }
  }

  @override
  void dispose() {
    // Giải phóng tất cả các controller cho móng
    for (final controller in _foundationLengthControllers) {
      controller.dispose();
    }

    _foundationColumnsController.dispose();

    // Giải phóng tất cả các controller cho tường
    for (final wall in _wallParams) {
      (wall['lengthController'] as TextEditingController).dispose();
      (wall['heightController'] as TextEditingController).dispose();
    }
    // Giải phóng tất cả các controller cho cửa sổ
    for (final window in _windowParams) {
      (window['widthController'] as TextEditingController).dispose();
      (window['heightController'] as TextEditingController).dispose();
      (window['quantityController'] as TextEditingController).dispose();
    }

    // Giải phóng tất cả các controller cho cửa đi
    for (final door in _doorParams) {
      (door['widthController'] as TextEditingController).dispose();
      (door['heightController'] as TextEditingController).dispose();
      (door['quantityController'] as TextEditingController).dispose();
    }

    // Giải phóng tất cả các controller cho cửa cuốn
    for (final rollingDoor in _rollingDoorParams) {
      (rollingDoor['widthController'] as TextEditingController).dispose();
      (rollingDoor['heightController'] as TextEditingController).dispose();
      (rollingDoor['quantityController'] as TextEditingController).dispose();
    }
    // Giải phóng tất cả các controller cho nhà vệ sinh
    for (final controller in _bathroomAreaControllers) {
      controller.dispose();
    }

    _gypsumCeilingAreaController.dispose();

    // Giải phóng tất cả các controller cho cầu thang
    for (final controller in _stairsStepsControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('Nhập thông số chi tiết:', style: TextStyle(fontSize: 16)),

          const SizedBox(height: 16),

          // Phần nhập thông số
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Thông số móng
                  _buildSectionTitle(
                    'Thông số móng',
                    onAddPressed: _addNewFoundation,
                  ),

                  // Danh sách động các trường nhập liệu cho móng
                  ..._buildFoundationInputs(),

                  const SizedBox(height: 8),
                  _buildNumberInput(
                    controller: _foundationColumnsController,
                    label: 'Số cột chịu lực',
                    suffix: 'cột',
                    isInteger: true,
                  ),

                  const SizedBox(height: 24),

                  // Thông số tường
                  _buildSectionTitle(
                    'Thông số tường',
                    onAddPressed: _addNewWall,
                  ),

                  // Danh sách động các trường nhập liệu cho tường
                  ..._buildWallInputs(),

                  const SizedBox(height: 24),

                  // Thông số cửa sổ
                  _buildSectionTitle(
                    'Thông số cửa sổ',
                    onAddPressed: _addNewWindow,
                  ),

                  // Danh sách động các trường nhập liệu cho cửa sổ
                  ..._buildDoorInputs(_windowParams, 'cửa sổ'),

                  const SizedBox(height: 16),

                  // Thông số cửa đi
                  _buildSectionTitle(
                    'Thông số cửa đi',
                    onAddPressed: _addNewDoor,
                  ),

                  // Danh sách động các trường nhập liệu cho cửa đi
                  ..._buildDoorInputs(_doorParams, 'cửa đi'),

                  const SizedBox(height: 16),

                  // Thông số cửa cuốn
                  _buildSectionTitle(
                    'Thông số cửa cuốn',
                    onAddPressed: _addNewRollingDoor,
                  ),

                  // Danh sách động các trường nhập liệu cho cửa cuốn
                  ..._buildDoorInputs(_rollingDoorParams, 'cửa cuốn'),

                  const SizedBox(height: 24),

                  // Thông số nhà vệ sinh
                  _buildSectionTitle(
                    'Thông số nhà vệ sinh',
                    onAddPressed: _addNewBathroom,
                  ),

                  // Danh sách động các trường nhập liệu cho nhà vệ sinh
                  ..._buildBathroomInputs(),

                  const SizedBox(height: 16),

                  // Thông số thạch cao
                  _buildSectionTitle(
                    'Thông số thạch cao',
                    onAddPressed: _addNewCeiling,
                  ),

                  // Trường nhập liệu cho thạch cao
                  _buildNumberInput(
                    controller: _gypsumCeilingAreaController,
                    label: 'Diện tích trần thạch cao',
                    suffix: 'm²',
                  ),

                  const SizedBox(height: 16),

                  // Thông số cầu thang
                  _buildSectionTitle(
                    'Thông số cầu thang',
                    onAddPressed: _addNewStairs,
                  ),

                  // Danh sách động các trường nhập liệu cho cầu thang
                  ..._buildStairsInputs(),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Nút điều hướng
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: widget.onBack,
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: const Text('Quay lại'),
                ),
              ),

              const SizedBox(width: 16),

              Expanded(
                child: GradientButton(text: 'Hoàn thành', onPressed: _complete),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Xây dựng tiêu đề phần
  Widget _buildSectionTitle(String title, {VoidCallback? onAddPressed}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Expanded(
            child: Text(
              title,
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
          ),
          if (onAddPressed != null)
            IconButton(
              icon: const Icon(Icons.add_circle, color: Colors.green),
              onPressed: onAddPressed,
              tooltip: 'Thêm mới',
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
            ),
        ],
      ),
    );
  }

  /// Xây dựng trường nhập số
  Widget _buildNumberInput({
    required TextEditingController controller,
    required String label,
    required String suffix,
    bool isInteger = false,
  }) {
    return TextField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        border: const OutlineInputBorder(),
        suffixText: suffix,
        hintText: '0',
      ),
      keyboardType: TextInputType.numberWithOptions(decimal: !isInteger),
      onTap: () {
        // Xóa giá trị 0 khi người dùng nhấn vào trường
        if (controller.text == '0' || controller.text == '0.0') {
          controller.clear();
        }
      },
      onChanged: (value) {
        // Chuyển đổi sang số
        if (value.isEmpty) {
          controller.text = isInteger ? '0' : '0.0';
          controller.selection = TextSelection.fromPosition(
            TextPosition(offset: controller.text.length),
          );
        } else if (isInteger) {
          final intValue = int.tryParse(value) ?? 0;
          controller.text = intValue.toString();
          controller.selection = TextSelection.fromPosition(
            TextPosition(offset: controller.text.length),
          );
        }
      },
    );
  }

  /// Xây dựng danh sách các trường nhập liệu cho móng
  List<Widget> _buildFoundationInputs() {
    final List<Widget> widgets = [];

    // Thêm các trường nhập liệu hiện có
    for (int i = 0; i < _foundationLengthControllers.length; i++) {
      widgets.add(
        Row(
          children: [
            Expanded(
              child: _buildNumberInput(
                controller: _foundationLengthControllers[i],
                label: 'Chiều dài móng ${i + 1}',
                suffix: 'm',
              ),
            ),
            if (_foundationLengthControllers.length > 1)
              IconButton(
                icon: const Icon(Icons.delete),
                onPressed: () {
                  setState(() {
                    // Xóa controller khỏi danh sách
                    final controller = _foundationLengthControllers.removeAt(i);
                    controller.dispose();
                  });
                },
                tooltip: 'Xóa móng này',
              ),
          ],
        ),
      );

      if (i < _foundationLengthControllers.length - 1) {
        widgets.add(const SizedBox(height: 8));
      }
    }

    return widgets;
  }

  /// Thêm móng mới
  void _addNewFoundation() {
    setState(() {
      _foundationLengthControllers.add(TextEditingController(text: '0'));
    });
  }

  /// Xây dựng danh sách các trường nhập liệu cho tường
  List<Widget> _buildWallInputs() {
    final List<Widget> widgets = [];

    // Thêm các trường nhập liệu hiện có
    for (int i = 0; i < _wallParams.length; i++) {
      final wall = _wallParams[i];
      final lengthController =
          wall['lengthController'] as TextEditingController;
      final heightController =
          wall['heightController'] as TextEditingController;

      widgets.add(
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      'Tường ${i + 1}',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ),
                  if (_wallParams.length > 1)
                    IconButton(
                      icon: const Icon(Icons.delete),
                      onPressed: () {
                        setState(() {
                          // Xóa tường khỏi danh sách
                          final removedWall = _wallParams.removeAt(i);
                          (removedWall['lengthController']
                                  as TextEditingController)
                              .dispose();
                          (removedWall['heightController']
                                  as TextEditingController)
                              .dispose();
                        });
                      },
                      tooltip: 'Xóa tường này',
                    ),
                ],
              ),

              const SizedBox(height: 8),

              // Loại tường
              DropdownButtonFormField<String>(
                decoration: const InputDecoration(
                  labelText: 'Loại tường',
                  border: OutlineInputBorder(),
                ),
                value: wall['type'] as String,
                items: const [
                  DropdownMenuItem(value: '10', child: Text('Tường 10cm')),
                  DropdownMenuItem(value: '20', child: Text('Tường 20cm')),
                ],
                onChanged: (value) {
                  setState(() {
                    wall['type'] = value;
                  });
                },
              ),

              const SizedBox(height: 8),

              // Số mặt chát
              DropdownButtonFormField<int>(
                decoration: const InputDecoration(
                  labelText: 'Số mặt chát',
                  border: OutlineInputBorder(),
                ),
                value: wall['plasterSides'] as int,
                items: const [
                  DropdownMenuItem(value: 1, child: Text('Chát 1 mặt')),
                  DropdownMenuItem(value: 2, child: Text('Chát 2 mặt')),
                ],
                onChanged: (value) {
                  setState(() {
                    wall['plasterSides'] = value;
                  });
                },
              ),

              const SizedBox(height: 8),

              // Chiều dài và chiều cao
              Row(
                children: [
                  Expanded(
                    child: _buildNumberInput(
                      controller: lengthController,
                      label: 'Chiều dài',
                      suffix: 'm',
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _buildNumberInput(
                      controller: heightController,
                      label: 'Chiều cao',
                      suffix: 'm',
                    ),
                  ),
                ],
              ),

              // Hiển thị diện tích tính được
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Text(
                  'Diện tích: ${(double.tryParse(lengthController.text) ?? 0) * (double.tryParse(heightController.text) ?? 0)} m²',
                  style: const TextStyle(fontStyle: FontStyle.italic),
                ),
              ),
            ],
          ),
        ),
      );

      if (i < _wallParams.length - 1) {
        widgets.add(const SizedBox(height: 16));
      }
    }

    return widgets;
  }

  /// Thêm tường mới
  void _addNewWall() {
    setState(() {
      _wallParams.add({
        'type': '10',
        'plasterSides': 2,
        'length': 0.0,
        'height': 0.0,
        'lengthController': TextEditingController(text: '0'),
        'heightController': TextEditingController(text: '0'),
      });
    });
  }

  /// Thêm cửa sổ mới
  void _addNewWindow() {
    setState(() {
      _windowParams.add({
        'width': 0.0,
        'height': 0.0,
        'quantity': 0,
        'widthController': TextEditingController(text: '0'),
        'heightController': TextEditingController(text: '0'),
        'quantityController': TextEditingController(text: '0'),
      });
    });
  }

  /// Thêm cửa đi mới
  void _addNewDoor() {
    setState(() {
      _doorParams.add({
        'width': 0.0,
        'height': 0.0,
        'quantity': 0,
        'widthController': TextEditingController(text: '0'),
        'heightController': TextEditingController(text: '0'),
        'quantityController': TextEditingController(text: '0'),
      });
    });
  }

  /// Thêm cửa cuốn mới
  void _addNewRollingDoor() {
    setState(() {
      _rollingDoorParams.add({
        'width': 0.0,
        'height': 0.0,
        'quantity': 0,
        'widthController': TextEditingController(text: '0'),
        'heightController': TextEditingController(text: '0'),
        'quantityController': TextEditingController(text: '0'),
      });
    });
  }

  /// Thêm nhà vệ sinh mới
  void _addNewBathroom() {
    setState(() {
      _bathroomAreaControllers.add(TextEditingController(text: '0'));
    });
  }

  /// Thêm thạch cao mới
  void _addNewCeiling() {
    // Hiện tại chỉ có một trường nhập liệu cho thạch cao
    // Có thể mở rộng trong tương lai để hỗ trợ nhiều khu vực thạch cao
  }

  /// Thêm cầu thang mới
  void _addNewStairs() {
    setState(() {
      _stairsStepsControllers.add(TextEditingController(text: '0'));
    });
  }

  /// Xây dựng danh sách các trường nhập liệu cho nhà vệ sinh
  List<Widget> _buildBathroomInputs() {
    final List<Widget> widgets = [];

    // Thêm các trường nhập liệu hiện có
    for (int i = 0; i < _bathroomAreaControllers.length; i++) {
      widgets.add(
        Row(
          children: [
            Expanded(
              child: _buildNumberInput(
                controller: _bathroomAreaControllers[i],
                label: 'Diện tích nhà vệ sinh ${i + 1}',
                suffix: 'm²',
              ),
            ),
            if (_bathroomAreaControllers.length > 1)
              IconButton(
                icon: const Icon(Icons.delete),
                onPressed: () {
                  setState(() {
                    // Xóa controller khỏi danh sách
                    final controller = _bathroomAreaControllers.removeAt(i);
                    controller.dispose();
                  });
                },
                tooltip: 'Xóa nhà vệ sinh này',
              ),
          ],
        ),
      );

      if (i < _bathroomAreaControllers.length - 1) {
        widgets.add(const SizedBox(height: 8));
      }
    }

    return widgets;
  }

  /// Xây dựng danh sách các trường nhập liệu cho cầu thang
  List<Widget> _buildStairsInputs() {
    final List<Widget> widgets = [];

    // Thêm các trường nhập liệu hiện có
    for (int i = 0; i < _stairsStepsControllers.length; i++) {
      widgets.add(
        Row(
          children: [
            Expanded(
              child: _buildNumberInput(
                controller: _stairsStepsControllers[i],
                label: 'Số bậc cầu thang ${i + 1}',
                suffix: 'bậc',
                isInteger: true,
              ),
            ),
            if (_stairsStepsControllers.length > 1)
              IconButton(
                icon: const Icon(Icons.delete),
                onPressed: () {
                  setState(() {
                    // Xóa controller khỏi danh sách
                    final controller = _stairsStepsControllers.removeAt(i);
                    controller.dispose();
                  });
                },
                tooltip: 'Xóa cầu thang này',
              ),
          ],
        ),
      );

      if (i < _stairsStepsControllers.length - 1) {
        widgets.add(const SizedBox(height: 8));
      }
    }

    return widgets;
  }

  /// Xây dựng danh sách các trường nhập liệu cho cửa
  List<Widget> _buildDoorInputs(
    List<Map<String, dynamic>> doorParams,
    String doorType,
  ) {
    final List<Widget> widgets = [];

    // Thêm các trường nhập liệu hiện có
    for (int i = 0; i < doorParams.length; i++) {
      final door = doorParams[i];
      final widthController = door['widthController'] as TextEditingController;
      final heightController =
          door['heightController'] as TextEditingController;
      final quantityController =
          door['quantityController'] as TextEditingController;

      widgets.add(
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      '$doorType ${i + 1}',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ),
                  if (doorParams.length > 1)
                    IconButton(
                      icon: const Icon(Icons.delete),
                      onPressed: () {
                        setState(() {
                          // Xóa cửa khỏi danh sách
                          final removedDoor = doorParams.removeAt(i);
                          (removedDoor['widthController']
                                  as TextEditingController)
                              .dispose();
                          (removedDoor['heightController']
                                  as TextEditingController)
                              .dispose();
                          (removedDoor['quantityController']
                                  as TextEditingController)
                              .dispose();
                        });
                      },
                      tooltip: 'Xóa $doorType này',
                    ),
                ],
              ),

              const SizedBox(height: 8),

              // Chiều rộng và chiều cao
              Row(
                children: [
                  Expanded(
                    child: _buildNumberInput(
                      controller: widthController,
                      label: 'Chiều rộng',
                      suffix: 'm',
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _buildNumberInput(
                      controller: heightController,
                      label: 'Chiều cao',
                      suffix: 'm',
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 8),

              // Số lượng
              _buildNumberInput(
                controller: quantityController,
                label: 'Số lượng',
                suffix: 'cái',
                isInteger: true,
              ),

              // Hiển thị diện tích tính được
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Text(
                  'Diện tích: ${(double.tryParse(widthController.text) ?? 0) * (double.tryParse(heightController.text) ?? 0) * (int.tryParse(quantityController.text) ?? 0)} m²',
                  style: const TextStyle(fontStyle: FontStyle.italic),
                ),
              ),
            ],
          ),
        ),
      );

      if (i < doorParams.length - 1) {
        widgets.add(const SizedBox(height: 16));
      }
    }

    return widgets;
  }

  /// Xử lý khi nhấn nút hoàn thành
  void _complete() {
    // Thu thập tất cả các chiều dài móng
    final List<double> foundationLengths =
        _foundationLengthControllers
            .map((controller) => double.tryParse(controller.text) ?? 0)
            .toList();

    // Thu thập thông tin tường
    final List<Map<String, dynamic>> walls = [];
    double totalWallArea = 0.0;

    for (final wall in _wallParams) {
      final lengthController =
          wall['lengthController'] as TextEditingController;
      final heightController =
          wall['heightController'] as TextEditingController;

      final length = double.tryParse(lengthController.text) ?? 0.0;
      final height = double.tryParse(heightController.text) ?? 0.0;
      final area = length * height;

      totalWallArea += area;

      walls.add({
        'type': wall['type'],
        'plasterSides': wall['plasterSides'],
        'length': length,
        'height': height,
        'area': area,
      });
    }

    // Thu thập thông tin cửa sổ
    final List<Map<String, dynamic>> windows = [];
    double totalWindowArea = 0.0;

    for (final window in _windowParams) {
      final widthController =
          window['widthController'] as TextEditingController;
      final heightController =
          window['heightController'] as TextEditingController;
      final quantityController =
          window['quantityController'] as TextEditingController;

      final width = double.tryParse(widthController.text) ?? 0.0;
      final height = double.tryParse(heightController.text) ?? 0.0;
      final quantity = int.tryParse(quantityController.text) ?? 0;
      final area = width * height * quantity;

      totalWindowArea += area;

      windows.add({
        'width': width,
        'height': height,
        'quantity': quantity,
        'area': area,
      });
    }

    // Thu thập thông tin cửa đi
    final List<Map<String, dynamic>> doors = [];
    double totalDoorArea = 0.0;

    for (final door in _doorParams) {
      final widthController = door['widthController'] as TextEditingController;
      final heightController =
          door['heightController'] as TextEditingController;
      final quantityController =
          door['quantityController'] as TextEditingController;

      final width = double.tryParse(widthController.text) ?? 0.0;
      final height = double.tryParse(heightController.text) ?? 0.0;
      final quantity = int.tryParse(quantityController.text) ?? 0;
      final area = width * height * quantity;

      totalDoorArea += area;

      doors.add({
        'width': width,
        'height': height,
        'quantity': quantity,
        'area': area,
      });
    }

    // Thu thập thông tin cửa cuốn
    final List<Map<String, dynamic>> rollingDoors = [];
    double totalRollingDoorArea = 0.0;

    for (final rollingDoor in _rollingDoorParams) {
      final widthController =
          rollingDoor['widthController'] as TextEditingController;
      final heightController =
          rollingDoor['heightController'] as TextEditingController;
      final quantityController =
          rollingDoor['quantityController'] as TextEditingController;

      final width = double.tryParse(widthController.text) ?? 0.0;
      final height = double.tryParse(heightController.text) ?? 0.0;
      final quantity = int.tryParse(quantityController.text) ?? 0;
      final area = width * height * quantity;

      totalRollingDoorArea += area;

      rollingDoors.add({
        'width': width,
        'height': height,
        'quantity': quantity,
        'area': area,
      });
    }

    // Tạo đối tượng DetailedParameters từ dữ liệu nhập
    final detailedParameters = DetailedParameters(
      foundation: {
        'lengths': foundationLengths,
        'columns': int.tryParse(_foundationColumnsController.text) ?? 0,
      },
      walls: {
        'walls': walls,
        'area':
            totalWallArea, // Tổng diện tích tường (tương thích với dữ liệu cũ)
      },
      doors: {
        'windows': windows,
        'doors': doors,
        'rollingDoors': rollingDoors,
        'windowArea': totalWindowArea, // Tương thích với dữ liệu cũ
        'doorArea': totalDoorArea, // Tương thích với dữ liệu cũ
        'rollingDoorArea': totalRollingDoorArea, // Tương thích với dữ liệu cũ
      },
      others: {
        'bathrooms':
            _bathroomAreaControllers.map((controller) {
              final area = double.tryParse(controller.text) ?? 0.0;
              return {'area': area};
            }).toList(),
        'gypsumCeilingArea':
            double.tryParse(_gypsumCeilingAreaController.text) ?? 0,
        'stairs':
            _stairsStepsControllers.map((controller) {
              final steps = int.tryParse(controller.text) ?? 0;
              return {'steps': steps};
            }).toList(),
        // Tương thích với dữ liệu cũ
        'bathroomCount': _bathroomAreaControllers.length,
        'stairsSteps': _stairsStepsControllers.fold<int>(
          0,
          (sum, controller) => sum + (int.tryParse(controller.text) ?? 0),
        ),
      },
    );

    // Tính toán vật liệu dựa trên thông số chi tiết
    final Map<String, dynamic> paramsMap = {
      'foundation': {
        'lengths': foundationLengths,
        'columns': int.tryParse(_foundationColumnsController.text) ?? 0,
      },
      'walls': {
        'walls': walls,
        'area':
            totalWallArea, // Tổng diện tích tường (tương thích với dữ liệu cũ)
      },
      'doors': {
        'windows': windows,
        'doors': doors,
        'rollingDoors': rollingDoors,
        'windowArea': totalWindowArea, // Tương thích với dữ liệu cũ
        'doorArea': totalDoorArea, // Tương thích với dữ liệu cũ
        'rollingDoorArea': totalRollingDoorArea, // Tương thích với dữ liệu cũ
      },
      'others': {
        'bathrooms':
            _bathroomAreaControllers.map((controller) {
              final area = double.tryParse(controller.text) ?? 0.0;
              return {'area': area};
            }).toList(),
        'gypsumCeilingArea':
            double.tryParse(_gypsumCeilingAreaController.text) ?? 0,
        'stairs':
            _stairsStepsControllers.map((controller) {
              final steps = int.tryParse(controller.text) ?? 0;
              return {'steps': steps};
            }).toList(),
        // Tương thích với dữ liệu cũ
        'bathroomCount': _bathroomAreaControllers.length,
        'stairsSteps': _stairsStepsControllers.fold<int>(
          0,
          (sum, controller) => sum + (int.tryParse(controller.text) ?? 0),
        ),
      },
    };

    // Lấy provider và dự án nháp
    final provider = Provider.of<ProjectProvider>(context, listen: false);
    final materialProvider = Provider.of<MaterialProvider>(
      context,
      listen: false,
    );
    final selectedMaterialIds =
        provider.draftProject?.selectedMaterialIds ?? [];

    // Lấy kích thước gạch từ MaterialProvider
    final brickDimensions = materialProvider.getBrickDimensions();

    // Tính toán vật liệu với danh sách vật liệu đã chọn, kích thước gạch và thông tin tầng
    final results = MaterialCalculator.calculateMaterialsFromDetailedParams(
      paramsMap,
      selectedMaterialIds: selectedMaterialIds,
      brickDimensions: brickDimensions,
      floors:
          provider.draftProject?.floors.map((floor) => floor.toMap()).toList(),
    );

    // Cập nhật dự án nháp trong provider
    provider.updateDraftDetailedParameters(detailedParameters);
    provider.updateDraftResults(results);

    // Hiển thị thông báo tính toán thành công
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Đã tính toán vật liệu thành công!'),
        duration: Duration(seconds: 2),
      ),
    );

    widget.onComplete();
  }
}
