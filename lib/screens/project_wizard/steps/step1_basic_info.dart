import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import '../../../models/project/project_provider.dart';
import '../../../widgets/gradient_button.dart';

/// Bước 1: <PERSON><PERSON><PERSON><PERSON> thông tin cơ bản
class Step1BasicInfo extends StatefulWidget {
  /// Callback khi nhấn nút tiếp tục
  final Function(String name, String location, String? imagePath) onContinue;

  const Step1BasicInfo({super.key, required this.onContinue});

  @override
  State<Step1BasicInfo> createState() => _Step1BasicInfoState();
}

class _Step1BasicInfoState extends State<Step1BasicInfo> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _locationController = TextEditingController();
  final _nameFocusNode = FocusNode();
  final _locationFocusNode = FocusNode();
  String? _imagePath;

  @override
  void initState() {
    super.initState();

    // L<PERSON>y dữ liệu từ dự án nháp nếu có
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final draftProject =
          Provider.of<ProjectProvider>(context, listen: false).draftProject;
      if (draftProject != null) {
        _nameController.text = draftProject.name;
        _locationController.text = draftProject.location;

        if (draftProject.imagePath != null &&
            draftProject.imagePath!.isNotEmpty) {
          setState(() {
            _imagePath = draftProject.imagePath;
          });
        }
      }
    });
  }

  @override
  void dispose() {
    _nameController.dispose();
    _locationController.dispose();
    _nameFocusNode.dispose();
    _locationFocusNode.dispose();
    super.dispose();
  }

  /// Ẩn bàn phím
  void _hideKeyboard() {
    _nameFocusNode.unfocus();
    _locationFocusNode.unfocus();
    FocusScope.of(context).unfocus();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Ảnh đại diện
            Center(
              child: GestureDetector(
                onTap: _pickImage,
                child: Container(
                  width: 150,
                  height: 150,
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(10),
                    image:
                        _imagePath != null
                            ? DecorationImage(
                              image: FileImage(File(_imagePath!)),
                              fit: BoxFit.cover,
                            )
                            : null,
                  ),
                  child:
                      _imagePath == null
                          ? const Icon(
                            Icons.add_a_photo,
                            size: 50,
                            color: Colors.grey,
                          )
                          : null,
                ),
              ),
            ),

            const SizedBox(height: 8),

            // Hướng dẫn thêm ảnh
            Center(
              child: TextButton(
                onPressed: _pickImage,
                child: const Text('Thêm ảnh đại diện (tùy chọn)'),
              ),
            ),

            const SizedBox(height: 24),

            // Tên dự án
            TextFormField(
              controller: _nameController,
              focusNode: _nameFocusNode,
              decoration: const InputDecoration(
                labelText: 'Tên công trình',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.home_work),
              ),
              textInputAction: TextInputAction.next,
              onFieldSubmitted: (_) {
                // Chuyển focus sang trường địa điểm khi nhấn Next
                FocusScope.of(context).requestFocus(_locationFocusNode);
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Vui lòng nhập tên công trình';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            // Địa điểm
            TextFormField(
              controller: _locationController,
              focusNode: _locationFocusNode,
              decoration: const InputDecoration(
                labelText: 'Địa điểm',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.location_on),
              ),
              textInputAction: TextInputAction.done,
              onFieldSubmitted: (_) {
                // Ẩn bàn phím khi nhấn Done
                _hideKeyboard();
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Vui lòng nhập địa điểm';
                }
                return null;
              },
            ),

            const SizedBox(height: 32),

            // Nút tiếp tục
            SizedBox(
              width: double.infinity,
              child: GradientButton(text: 'Tiếp tục', onPressed: _continue),
            ),
          ],
        ),
      ),
    );
  }

  /// Chọn ảnh từ thư viện hoặc camera
  Future<void> _pickImage() async {
    final picker = ImagePicker();

    // Hiển thị dialog chọn nguồn ảnh
    showModalBottomSheet(
      context: context,
      builder:
          (context) => SafeArea(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  leading: const Icon(Icons.photo_library),
                  title: const Text('Chọn từ thư viện'),
                  onTap: () async {
                    Navigator.pop(context);
                    final pickedFile = await picker.pickImage(
                      source: ImageSource.gallery,
                    );
                    if (pickedFile != null) {
                      setState(() {
                        _imagePath = pickedFile.path;
                      });
                    }
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.camera_alt),
                  title: const Text('Chụp ảnh mới'),
                  onTap: () async {
                    Navigator.pop(context);
                    final pickedFile = await picker.pickImage(
                      source: ImageSource.camera,
                    );
                    if (pickedFile != null) {
                      setState(() {
                        _imagePath = pickedFile.path;
                      });
                    }
                  },
                ),
                if (_imagePath != null)
                  ListTile(
                    leading: const Icon(Icons.delete, color: Colors.red),
                    title: const Text(
                      'Xóa ảnh',
                      style: TextStyle(color: Colors.red),
                    ),
                    onTap: () {
                      Navigator.pop(context);
                      setState(() {
                        _imagePath = null;
                      });
                    },
                  ),
              ],
            ),
          ),
    );
  }

  /// Xử lý khi nhấn nút tiếp tục
  void _continue() {
    // Ẩn bàn phím trước khi xác thực form
    _hideKeyboard();

    if (_formKey.currentState!.validate()) {
      // Cập nhật dự án nháp trong provider
      Provider.of<ProjectProvider>(context, listen: false).updateDraftBasicInfo(
        _nameController.text,
        _locationController.text,
        _imagePath,
      );

      widget.onContinue(
        _nameController.text,
        _locationController.text,
        _imagePath,
      );
    }
  }
}
