import 'package:flutter/material.dart';
import '../../../models/project/project_model.dart';

/// Bước 3: Chọn loại kết cấu
class Step3StructureType extends StatefulWidget {
  /// Callback khi nhấn nút tiếp tục
  final Function(StructureType structureType) onContinue;
  
  /// Callback khi nhấn nút quay lại
  final VoidCallback onBack;

  const Step3StructureType({
    super.key,
    required this.onContinue,
    required this.onBack,
  });

  @override
  State<Step3StructureType> createState() => _Step3StructureTypeState();
}

class _Step3StructureTypeState extends State<Step3StructureType> {
  StructureType? _selectedType;
  
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Chọn loại kết cấu cho công trình của bạn:',
            style: TextStyle(fontSize: 16),
          ),
          
          const SizedBox(height: 24),
          
          // Danh sách loại kết cấu
          Expanded(
            child: ListView(
              children: [
                _buildStructureTypeCard(
                  type: StructureType.concrete,
                  title: 'Bê tông cốt thép',
                  description: 'Bền vững, chịu lực tốt, phù hợp với nhiều loại công trình.',
                  icon: Icons.view_in_ar,
                ),
                
                _buildStructureTypeCard(
                  type: StructureType.steel,
                  title: 'Khung thép',
                  description: 'Nhẹ, lắp dựng nhanh, phù hợp với công trình công nghiệp.',
                  icon: Icons.architecture,
                ),
                
                _buildStructureTypeCard(
                  type: StructureType.brick,
                  title: 'Gạch',
                  description: 'Kinh tế, dễ thi công, phù hợp với công trình nhỏ.',
                  icon: Icons.grid_on,
                ),
                
                _buildStructureTypeCard(
                  type: StructureType.wood,
                  title: 'Gỗ',
                  description: 'Thân thiện môi trường, phù hợp với công trình nhỏ, biệt thự.',
                  icon: Icons.forest,
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Nút điều hướng
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: widget.onBack,
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: const Text('Quay lại'),
                ),
              ),
              
              const SizedBox(width: 16),
              
              Expanded(
                child: ElevatedButton(
                  onPressed: _selectedType != null
                      ? () => widget.onContinue(_selectedType!)
                      : null,
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: const Text('Tiếp tục'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
  
  /// Xây dựng card loại kết cấu
  Widget _buildStructureTypeCard({
    required StructureType type,
    required String title,
    required String description,
    required IconData icon,
  }) {
    final isSelected = _selectedType == type;
    
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: isSelected ? 4 : 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(
          color: isSelected ? Theme.of(context).primaryColor : Colors.transparent,
          width: 2,
        ),
      ),
      child: InkWell(
        onTap: () {
          setState(() {
            _selectedType = type;
          });
        },
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: isSelected
                      ? Theme.of(context).primaryColor
                      : Colors.grey[200],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  size: 30,
                  color: isSelected ? Colors.white : Colors.grey[600],
                ),
              ),
              
              const SizedBox(width: 16),
              
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: isSelected
                            ? Theme.of(context).primaryColor
                            : null,
                      ),
                    ),
                    
                    const SizedBox(height: 4),
                    
                    Text(
                      description,
                      style: TextStyle(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              
              if (isSelected)
                Icon(
                  Icons.check_circle,
                  color: Theme.of(context).primaryColor,
                ),
            ],
          ),
        ),
      ),
    );
  }
}
