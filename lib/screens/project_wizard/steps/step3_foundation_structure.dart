import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../models/project/project_provider.dart';
import '../../../models/project/project_model.dart';
import '../../../models/project/building_model.dart';
import '../../../widgets/gradient_button.dart';

/// Bước 3: Thông tin móng và kết cấu
class Step3FoundationStructure extends StatefulWidget {
  /// Callback khi nhấn nút tiếp tục
  final VoidCallback onContinue;

  /// Callback khi nhấn nút quay lại
  final VoidCallback onBack;

  const Step3FoundationStructure({
    super.key,
    required this.onContinue,
    required this.onBack,
  });

  @override
  State<Step3FoundationStructure> createState() =>
      _Step3FoundationStructureState();
}

class _Step3FoundationStructureState extends State<Step3FoundationStructure> {
  FoundationType? _selectedFoundationType;
  StructureType? _selectedStructureType;

  @override
  void initState() {
    super.initState();

    // <PERSON><PERSON>y dữ liệu từ dự án nháp nếu có
    final draftProject =
        Provider.of<ProjectProvider>(context, listen: false).draftProject;
    if (draftProject != null) {
      _selectedFoundationType = draftProject.foundationType;
      _selectedStructureType = draftProject.structureType;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Chọn loại móng và kết cấu:',
            style: TextStyle(fontSize: 16),
          ),

          const SizedBox(height: 24),

          // Phần móng
          const Text(
            'Loại móng',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),

          const SizedBox(height: 16),

          // Danh sách loại móng
          Expanded(
            flex: 1,
            child: ListView(
              children: [
                _buildFoundationTypeCard(
                  type: FoundationType.strip,
                  title: 'Móng băng',
                  description:
                      'Phù hợp với công trình nhỏ, tải trọng phân bố đều.',
                  icon: Icons.border_bottom,
                ),

                _buildFoundationTypeCard(
                  type: FoundationType.isolated,
                  title: 'Móng đơn',
                  description: 'Phù hợp với công trình có cột riêng biệt.',
                  icon: Icons.grid_4x4,
                ),

                _buildFoundationTypeCard(
                  type: FoundationType.pile,
                  title: 'Móng cọc',
                  description: 'Phù hợp với công trình lớn, đất yếu.',
                  icon: Icons.view_column,
                ),

                _buildFoundationTypeCard(
                  type: FoundationType.raft,
                  title: 'Móng bè',
                  description: 'Phù hợp với công trình lớn, tải trọng lớn.',
                  icon: Icons.view_quilt,
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Phần kết cấu
          const Text(
            'Loại kết cấu',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),

          const SizedBox(height: 16),

          // Danh sách loại kết cấu
          Expanded(
            flex: 1,
            child: ListView(
              children: [
                _buildStructureTypeCard(
                  type: StructureType.concrete,
                  title: 'Bê tông cốt thép',
                  description:
                      'Bền vững, chịu lực tốt, phù hợp với nhiều loại công trình.',
                  icon: Icons.view_in_ar,
                ),

                _buildStructureTypeCard(
                  type: StructureType.steel,
                  title: 'Khung thép',
                  description:
                      'Nhẹ, lắp dựng nhanh, phù hợp với công trình công nghiệp.',
                  icon: Icons.architecture,
                ),

                _buildStructureTypeCard(
                  type: StructureType.brick,
                  title: 'Gạch',
                  description:
                      'Kinh tế, dễ thi công, phù hợp với công trình nhỏ.',
                  icon: Icons.grid_on,
                ),

                _buildStructureTypeCard(
                  type: StructureType.wood,
                  title: 'Gỗ',
                  description:
                      'Thân thiện môi trường, phù hợp với công trình nhỏ, biệt thự.',
                  icon: Icons.forest,
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Nút điều hướng
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: widget.onBack,
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: const Text('Quay lại'),
                ),
              ),

              const SizedBox(width: 16),

              Expanded(
                child:
                    _selectedFoundationType != null &&
                            _selectedStructureType != null
                        ? GradientButton(text: 'Tiếp tục', onPressed: _continue)
                        : ElevatedButton(
                          onPressed: null,
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                          ),
                          child: const Text('Tiếp tục'),
                        ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Xây dựng card loại móng
  Widget _buildFoundationTypeCard({
    required FoundationType type,
    required String title,
    required String description,
    required IconData icon,
  }) {
    final isSelected = _selectedFoundationType == type;

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: isSelected ? 4 : 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(
          color:
              isSelected ? Theme.of(context).primaryColor : Colors.transparent,
          width: 2,
        ),
      ),
      child: InkWell(
        onTap: () {
          setState(() {
            _selectedFoundationType = type;
          });
        },
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color:
                      isSelected
                          ? Theme.of(context).primaryColor
                          : Colors.grey[200],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  size: 30,
                  color: isSelected ? Colors.white : Colors.grey[600],
                ),
              ),

              const SizedBox(width: 16),

              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color:
                            isSelected ? Theme.of(context).primaryColor : null,
                      ),
                    ),

                    const SizedBox(height: 4),

                    Text(
                      description,
                      style: TextStyle(color: Colors.grey[600]),
                    ),
                  ],
                ),
              ),

              if (isSelected)
                Icon(Icons.check_circle, color: Theme.of(context).primaryColor),
            ],
          ),
        ),
      ),
    );
  }

  /// Xây dựng card loại kết cấu
  Widget _buildStructureTypeCard({
    required StructureType type,
    required String title,
    required String description,
    required IconData icon,
  }) {
    final isSelected = _selectedStructureType == type;

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: isSelected ? 4 : 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(
          color:
              isSelected ? Theme.of(context).primaryColor : Colors.transparent,
          width: 2,
        ),
      ),
      child: InkWell(
        onTap: () {
          setState(() {
            _selectedStructureType = type;
          });
        },
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color:
                      isSelected
                          ? Theme.of(context).primaryColor
                          : Colors.grey[200],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  size: 30,
                  color: isSelected ? Colors.white : Colors.grey[600],
                ),
              ),

              const SizedBox(width: 16),

              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color:
                            isSelected ? Theme.of(context).primaryColor : null,
                      ),
                    ),

                    const SizedBox(height: 4),

                    Text(
                      description,
                      style: TextStyle(color: Colors.grey[600]),
                    ),
                  ],
                ),
              ),

              if (isSelected)
                Icon(Icons.check_circle, color: Theme.of(context).primaryColor),
            ],
          ),
        ),
      ),
    );
  }

  /// Xử lý khi nhấn nút tiếp tục
  void _continue() {
    // Cập nhật dự án nháp trong provider
    final provider = Provider.of<ProjectProvider>(context, listen: false);
    provider.updateDraftFoundationType(_selectedFoundationType);
    provider.updateDraftStructureType(_selectedStructureType);

    widget.onContinue();
  }
}
