import 'package:flutter/material.dart';
import 'package:nha_save/models/project/building_model.dart';

/// Bước 2: Chọn loại móng
class Step2FoundationType extends StatefulWidget {
  /// Callback khi nhấn nút tiếp tục
  final Function(FoundationType foundationType) onContinue;

  /// Callback khi nhấn nút quay lại
  final VoidCallback onBack;

  const Step2FoundationType({
    super.key,
    required this.onContinue,
    required this.onBack,
  });

  @override
  State<Step2FoundationType> createState() => _Step2FoundationTypeState();
}

class _Step2FoundationTypeState extends State<Step2FoundationType> {
  FoundationType? _selectedType;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Chọn loại móng cho công trình của bạn:',
            style: TextStyle(fontSize: 16),
          ),

          const SizedBox(height: 24),

          // Danh sách loại móng
          Expanded(
            child: ListView(
              children: [
                _buildFoundationTypeCard(
                  type: FoundationType.strip,
                  title: 'Móng băng',
                  description:
                      'Phù hợp với công trình nhỏ, tải trọng phân bố đều.',
                  icon: Icons.border_bottom,
                ),

                _buildFoundationTypeCard(
                  type: FoundationType.isolated,
                  title: 'Móng đơn',
                  description: 'Phù hợp với công trình có cột riêng biệt.',
                  icon: Icons.grid_4x4,
                ),

                _buildFoundationTypeCard(
                  type: FoundationType.pile,
                  title: 'Móng cọc',
                  description: 'Phù hợp với công trình lớn, đất yếu.',
                  icon: Icons.view_column,
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Nút điều hướng
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: widget.onBack,
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: const Text('Quay lại'),
                ),
              ),

              const SizedBox(width: 16),

              Expanded(
                child: ElevatedButton(
                  onPressed:
                      _selectedType != null
                          ? () {
                            // Ẩn bàn phím nếu đang hiển thị
                            FocusScope.of(context).unfocus();
                            widget.onContinue(_selectedType!);
                          }
                          : null,
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: const Text('Tiếp tục'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Xây dựng card loại móng
  Widget _buildFoundationTypeCard({
    required FoundationType type,
    required String title,
    required String description,
    required IconData icon,
  }) {
    final isSelected = _selectedType == type;

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: isSelected ? 4 : 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(
          color:
              isSelected ? Theme.of(context).primaryColor : Colors.transparent,
          width: 2,
        ),
      ),
      child: InkWell(
        onTap: () {
          // Ẩn bàn phím nếu đang hiển thị
          FocusScope.of(context).unfocus();

          setState(() {
            _selectedType = type;
          });
        },
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color:
                      isSelected
                          ? Theme.of(context).primaryColor
                          : Colors.grey[200],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  size: 30,
                  color: isSelected ? Colors.white : Colors.grey[600],
                ),
              ),

              const SizedBox(width: 16),

              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color:
                            isSelected ? Theme.of(context).primaryColor : null,
                      ),
                    ),

                    const SizedBox(height: 4),

                    Text(
                      description,
                      style: TextStyle(color: Colors.grey[600]),
                    ),
                  ],
                ),
              ),

              if (isSelected)
                Icon(Icons.check_circle, color: Theme.of(context).primaryColor),
            ],
          ),
        ),
      ),
    );
  }
}
