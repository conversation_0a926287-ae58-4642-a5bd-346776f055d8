import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../models/project/project_provider.dart';
import '../../../models/project/building_model.dart';
import '../../../widgets/gradient_button.dart';

/// Bước 2: Thông tin tầng và mái
class Step2FloorsRoof extends StatefulWidget {
  /// Callback khi nhấn nút tiếp tục
  final VoidCallback onContinue;

  /// Callback khi nhấn nút quay lại
  final VoidCallback onBack;

  const Step2FloorsRoof({
    super.key,
    required this.onContinue,
    required this.onBack,
  });

  @override
  State<Step2FloorsRoof> createState() => _Step2FloorsRoofState();
}

class _Step2FloorsRoofState extends State<Step2FloorsRoof> {
  final List<Floor> _floors = [];
  Roof? _roof;

  // Danh sách các FocusNode cho các trường nhập liệu
  final List<FocusNode> _areaFocusNodes = [];
  final List<FocusNode> _heightFocusNodes = [];
  final _roofAreaFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();

    // Lấy dữ liệu từ dự án nháp nếu có
    final draftProject =
        Provider.of<ProjectProvider>(context, listen: false).draftProject;
    if (draftProject != null) {
      if (draftProject.floors.isNotEmpty) {
        _floors.addAll(draftProject.floors);
      } else {
        _floors.add(Floor(number: 1, area: 0, height: 0));
      }

      _roof = draftProject.roof;
    } else {
      _floors.add(Floor(number: 1, area: 0, height: 0));
    }

    // Khởi tạo FocusNode cho mỗi tầng
    _initializeFocusNodes();
  }

  /// Khởi tạo FocusNode cho các trường nhập liệu
  void _initializeFocusNodes() {
    // Xóa các FocusNode cũ
    for (var node in _areaFocusNodes) {
      node.dispose();
    }
    for (var node in _heightFocusNodes) {
      node.dispose();
    }

    _areaFocusNodes.clear();
    _heightFocusNodes.clear();

    // Tạo FocusNode mới cho mỗi tầng
    for (int i = 0; i < _floors.length; i++) {
      _areaFocusNodes.add(FocusNode());
      _heightFocusNodes.add(FocusNode());
    }
  }

  @override
  void dispose() {
    // Giải phóng tất cả FocusNode
    for (var node in _areaFocusNodes) {
      node.dispose();
    }
    for (var node in _heightFocusNodes) {
      node.dispose();
    }
    _roofAreaFocusNode.dispose();
    super.dispose();
  }

  /// Ẩn bàn phím
  void _hideKeyboard() {
    // Ẩn bàn phím bằng cách unfocus tất cả các FocusNode
    for (var node in _areaFocusNodes) {
      node.unfocus();
    }
    for (var node in _heightFocusNodes) {
      node.unfocus();
    }
    _roofAreaFocusNode.unfocus();
    FocusScope.of(context).unfocus();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Nhập thông tin về tầng và mái:',
            style: TextStyle(fontSize: 16),
          ),

          const SizedBox(height: 16),

          // Danh sách tầng
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Tiêu đề phần tầng
                  Row(
                    children: [
                      const Text(
                        'Thông tin tầng',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      IconButton(
                        icon: const Icon(Icons.add_circle),
                        onPressed: _addFloor,
                        tooltip: 'Thêm tầng',
                      ),
                    ],
                  ),

                  const SizedBox(height: 8),

                  // Danh sách tầng
                  ..._floors.map((floor) => _buildFloorItem(floor)),

                  const SizedBox(height: 24),

                  // Tiêu đề phần mái
                  const Text(
                    'Thông tin mái',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),

                  const SizedBox(height: 8),

                  // Loại mái
                  DropdownButtonFormField<RoofType>(
                    decoration: const InputDecoration(
                      labelText: 'Loại mái',
                      border: OutlineInputBorder(),
                    ),
                    value: _roof?.type ?? RoofType.flat,
                    items:
                        RoofType.values.map((type) {
                          return DropdownMenuItem<RoofType>(
                            value: type,
                            child: Text(_getRoofTypeName(type)),
                          );
                        }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          if (_roof == null) {
                            _roof = Roof(type: value, area: 0);
                          } else {
                            _roof!.type = value;
                          }
                        });
                      }
                    },
                  ),

                  const SizedBox(height: 16),

                  // Diện tích mái
                  TextFormField(
                    initialValue:
                        (_roof?.area == 0 || _roof?.area == null)
                            ? ''
                            : _roof?.area.toString(),
                    focusNode: _roofAreaFocusNode,
                    decoration: const InputDecoration(
                      labelText: 'Diện tích mái (m²)',
                      border: OutlineInputBorder(),
                      suffixText: 'm²',
                      hintText: '0',
                    ),
                    keyboardType: const TextInputType.numberWithOptions(
                      decimal: true,
                    ),
                    textInputAction: TextInputAction.done,
                    onFieldSubmitted: (_) {
                      // Ẩn bàn phím khi nhấn Done
                      _hideKeyboard();
                    },
                    onChanged: (value) {
                      final area =
                          value.isEmpty ? 0.0 : double.tryParse(value) ?? 0.0;
                      setState(() {
                        if (_roof == null) {
                          _roof = Roof(type: RoofType.flat, area: area);
                        } else {
                          _roof!.area = area;
                        }
                      });
                    },
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Nút điều hướng
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: widget.onBack,
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: const Text('Quay lại'),
                ),
              ),

              const SizedBox(width: 16),

              Expanded(
                child: GradientButton(text: 'Tiếp tục', onPressed: _continue),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Xây dựng item tầng
  Widget _buildFloorItem(Floor floor) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'Tầng ${floor.number}',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                if (_floors.length > 1)
                  IconButton(
                    icon: const Icon(Icons.delete, color: Colors.red),
                    onPressed: () => _removeFloor(floor),
                    tooltip: 'Xóa tầng',
                  ),
              ],
            ),

            const SizedBox(height: 16),

            // Diện tích tầng
            TextFormField(
              initialValue: floor.area == 0 ? '' : floor.area.toString(),
              focusNode: _areaFocusNodes[_floors.indexOf(floor)],
              decoration: const InputDecoration(
                labelText: 'Diện tích (m²)',
                border: OutlineInputBorder(),
                suffixText: 'm²',
                hintText: '0',
              ),
              keyboardType: const TextInputType.numberWithOptions(
                decimal: true,
              ),
              textInputAction: TextInputAction.next,
              onFieldSubmitted: (_) {
                // Chuyển focus sang trường chiều cao
                FocusScope.of(
                  context,
                ).requestFocus(_heightFocusNodes[_floors.indexOf(floor)]);
              },
              onChanged: (value) {
                final area =
                    value.isEmpty ? 0.0 : double.tryParse(value) ?? 0.0;
                setState(() {
                  floor.area = area;
                });
              },
            ),

            const SizedBox(height: 16),

            // Chiều cao tầng
            TextFormField(
              initialValue: floor.height == 0 ? '' : floor.height.toString(),
              focusNode: _heightFocusNodes[_floors.indexOf(floor)],
              decoration: const InputDecoration(
                labelText: 'Chiều cao (m)',
                border: OutlineInputBorder(),
                suffixText: 'm',
                hintText: '0',
              ),
              keyboardType: const TextInputType.numberWithOptions(
                decimal: true,
              ),
              textInputAction: TextInputAction.done,
              onFieldSubmitted: (_) {
                // Ẩn bàn phím khi nhấn Done
                _hideKeyboard();
              },
              onChanged: (value) {
                final height =
                    value.isEmpty ? 0.0 : double.tryParse(value) ?? 0.0;
                setState(() {
                  floor.height = height;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  /// Thêm tầng mới
  void _addFloor() {
    setState(() {
      _floors.add(Floor(number: _floors.length + 1, area: 0, height: 0));

      // Thêm FocusNode cho tầng mới
      _areaFocusNodes.add(FocusNode());
      _heightFocusNodes.add(FocusNode());
    });
  }

  /// Xóa tầng
  void _removeFloor(Floor floor) {
    setState(() {
      // Lấy index của tầng cần xóa
      final index = _floors.indexOf(floor);
      if (index != -1) {
        // Xóa FocusNode tương ứng
        if (index < _areaFocusNodes.length) {
          _areaFocusNodes[index].dispose();
          _areaFocusNodes.removeAt(index);
        }
        if (index < _heightFocusNodes.length) {
          _heightFocusNodes[index].dispose();
          _heightFocusNodes.removeAt(index);
        }
      }

      _floors.remove(floor);

      // Cập nhật số thứ tự tầng
      for (int i = 0; i < _floors.length; i++) {
        _floors[i] = Floor(
          number: i + 1,
          area: _floors[i].area,
          height: _floors[i].height,
        );
      }
    });
  }

  /// Lấy tên loại mái
  String _getRoofTypeName(RoofType type) {
    switch (type) {
      case RoofType.flat:
        return 'Mái bằng';
      case RoofType.metal:
        return 'Mái tôn';
      case RoofType.tile:
        return 'Mái ngói';
    }
  }

  /// Xử lý khi nhấn nút tiếp tục
  void _continue() {
    // Ẩn bàn phím trước khi tiếp tục
    _hideKeyboard();

    // Cập nhật dự án nháp trong provider
    final provider = Provider.of<ProjectProvider>(context, listen: false);
    provider.updateDraftFloors(_floors);
    provider.updateDraftRoof(_roof);

    widget.onContinue();
  }
}
