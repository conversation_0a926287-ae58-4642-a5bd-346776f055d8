import 'material_model.dart';

/// Lớp vật liệu thạch cao
class Gypsum extends Material {
  /// Hàm khởi tạo
  Gypsum({required super.pricePerUnit})
    : super(
        name: 'Thạch cao',
        type: MaterialType.gypsum,
        measurementUnit: MeasurementUnit.squareMeter,
      );

  @override
  double calculateQuantity(Map<String, dynamic> parameters) {
    // Trích xuất các thông số
    // Ưu tiên sử dụng giá trị gypsumCeilingArea từ Step5 nếu có
    if (parameters.containsKey('gypsumCeilingArea')) {
      return parameters['gypsumCeilingArea'] ?? 0.0;
    }

    // Nếu không có, sử dụng tham số area (tương thích ngược)
    final double area = parameters['area'] ?? 0.0;
    return area;
  }

  @override
  List<String> get requiredParameters => ['gypsumCeilingArea'];

  @override
  String get unit => 'm²';

  @override
  Material copyWith({double? pricePerUnit}) {
    // Tạo bản sao với giá mới hoặc giữ nguyên giá hiện tại
    return Gypsum(pricePerUnit: pricePerUnit ?? this.pricePerUnit);
  }
}
