import 'material_model.dart';

/// Lớp vật liệu cửa nhựa composite
class CompositeDoor extends Material {
  /// Hàm khởi tạo
  CompositeDoor({required super.pricePerUnit})
      : super(
          name: 'Cửa nhựa composite',
          type: MaterialType.compositeDoor,
          measurementUnit: MeasurementUnit.set,
        );

  @override
  double calculateQuantity(Map<String, dynamic> parameters) {
    // Trích xuất các thông số
    final int quantity = parameters['quantity'] ?? 0;
    
    // Số lượng bộ cửa
    return quantity.toDouble();
  }

  @override
  List<String> get requiredParameters => ['quantity'];

  @override
  String get unit => 'bộ';

  @override
  Material copyWith({double? pricePerUnit}) {
    // Tạo bản sao với giá mới hoặc giữ nguyên giá hiện tại
    return CompositeDoor(pricePerUnit: pricePerUnit ?? this.pricePerUnit);
  }
}
