import 'material_model.dart';

/// Loại tường
enum WallType {
  wall10cm1Side, // Tường 10cm chát 1 mặt
  wall10cm2Side, // Tường 10cm chát 2 mặt
  wall20cm1Side, // Tường 20cm chát 1 mặt
  wall20cm2Side, // Tường 20cm chát 2 mặt
}

/// Lớp vật liệu gạch xây
class Brick extends Material {
  /// Kích thước của viên gạch theo mét
  double brickLength;
  double brickWidth;
  double brickHeight;

  /// Hàm khởi tạo
  Brick({
    required super.pricePerUnit,
    this.brickLength = 0.2,
    this.brickWidth = 0.1,
    this.brickHeight = 0.05,
  }) : super(
         name: 'Gạch xây',
         type: MaterialType.brick,
         measurementUnit: MeasurementUnit.piece,
       );

  @override
  double calculateQuantity(Map<String, dynamic> parameters) {
    // Kiểm tra xem có thông số loại tường không
    if (parameters.containsKey('wallType')) {
      return _calculateByWallType(parameters);
    } else {
      return _calculateByDimensions(parameters);
    }
  }

  /// Tính số lượng gạch dựa trên kích thước
  double _calculateByDimensions(Map<String, dynamic> parameters) {
    // Trích xuất các thông số
    final double length = parameters['length'] ?? 0.0;
    final double width = parameters['width'] ?? 0.0;
    final double height = parameters['height'] ?? 0.0;

    // Tính thể tích tường
    final double wallVolume = length * width * height;

    // Tính thể tích viên gạch
    final double brickVolume = brickLength * brickWidth * brickHeight;

    // Tính số lượng gạch cần thiết
    // Thêm 5% cho hao hụt
    return (wallVolume / brickVolume) * 1.05;
  }

  /// Tính số lượng gạch dựa trên loại tường
  double _calculateByWallType(Map<String, dynamic> parameters) {
    // Trích xuất các thông số
    final WallType wallType = parameters['wallType'] as WallType;
    final double length = parameters['length'] ?? 0.0;
    final double height = parameters['height'] ?? 0.0;

    // Tính diện tích tường
    final double wallArea = length * height;

    // Số lượng gạch trên mỗi m² tường
    double bricksPerSquareMeter;

    switch (wallType) {
      case WallType.wall10cm1Side:
        bricksPerSquareMeter = 65; // Tường 10cm chát 1 mặt
        break;
      case WallType.wall10cm2Side:
        bricksPerSquareMeter = 65; // Tường 10cm chát 2 mặt (số gạch không đổi)
        break;
      case WallType.wall20cm1Side:
        bricksPerSquareMeter = 130; // Tường 20cm chát 1 mặt
        break;
      case WallType.wall20cm2Side:
        bricksPerSquareMeter = 130; // Tường 20cm chát 2 mặt (số gạch không đổi)
        break;
    }

    // Tính số lượng gạch cần thiết
    // Thêm 3% cho hao hụt
    return wallArea * bricksPerSquareMeter * 1.03;
  }

  @override
  List<String> get requiredParameters => ['length', 'width', 'height'];

  @override
  String get unit => 'viên';

  @override
  Material copyWith({
    double? pricePerUnit,
    double? brickLength,
    double? brickWidth,
    double? brickHeight,
  }) {
    // Tạo bản sao với giá và kích thước mới hoặc giữ nguyên giá trị hiện tại
    return Brick(
      pricePerUnit: pricePerUnit ?? this.pricePerUnit,
      brickLength: brickLength ?? this.brickLength,
      brickWidth: brickWidth ?? this.brickWidth,
      brickHeight: brickHeight ?? this.brickHeight,
    );
  }
}
