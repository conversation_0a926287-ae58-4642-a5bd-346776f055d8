import 'material_model.dart';

/// Lớp vật liệu xi măng
class Cement extends Material {
  /// Hàm khởi tạo
  Cement({required super.pricePerUnit})
      : super(
          name: 'Xi măng',
          type: MaterialType.cement,
          measurementUnit: MeasurementUnit.ton,
        );

  @override
  double calculateQuantity(Map<String, dynamic> parameters) {
    // Trích xuất các thông số
    final double wallVolume = parameters['wallVolume'] ?? 0.0;

    // Tính lượng xi măng (0.005 tấn xi măng trên mỗi m³ tường)
    // 5kg = 0.005 tấn
    return wallVolume * 0.005;
  }

  @override
  List<String> get requiredParameters => ['wallVolume'];

  @override
  String get unit => 'tấn';

  @override
  Material copyWith({double? pricePerUnit}) {
    // Tạo bản sao với giá mới hoặc giữ nguyên giá hiện tại
    return Cement(pricePerUnit: pricePerUnit ?? this.pricePerUnit);
  }
}
