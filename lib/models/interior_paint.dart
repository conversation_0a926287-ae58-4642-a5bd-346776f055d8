import 'material_model.dart';

/// Lớp vật liệu sơn nội thất
class InteriorPaint extends Material {
  /// Đ<PERSON><PERSON> mức sơn trên mỗi m² (lít)
  static const double paintPerSquareMeter = 0.15;

  /// Hàm khởi tạo
  InteriorPaint({required super.pricePerUnit})
      : super(
          name: 'Sơn nội thất',
          type: MaterialType.interiorPaint,
          measurementUnit: MeasurementUnit.squareMeter,
        );

  @override
  double calculateQuantity(Map<String, dynamic> parameters) {
    // Trích xuất các thông số
    final double area = parameters['area'] ?? 0.0;
    
    // Diện tích sơn bằng diện tích đã nhập
    return area;
  }

  @override
  List<String> get requiredParameters => ['area'];

  @override
  String get unit => 'm²';

  @override
  Material copyWith({double? pricePerUnit}) {
    // Tạo bản sao với giá mới hoặc giữ nguyên giá hiện tại
    return InteriorPaint(pricePerUnit: pricePerUnit ?? this.pricePerUnit);
  }
}
