import 'material_model.dart';

/// Lớp vật liệu cửa nhôm Xingfa
class AluminumDoor extends Material {
  /// Hàm khởi tạo
  AluminumDoor({required super.pricePerUnit})
      : super(
          name: 'Cửa nhôm Xingfa',
          type: MaterialType.aluminumDoor,
          measurementUnit: MeasurementUnit.squareMeter,
        );

  @override
  double calculateQuantity(Map<String, dynamic> parameters) {
    // Trích xuất các thông số
    final double width = parameters['width'] ?? 0.0;
    final double height = parameters['height'] ?? 0.0;
    
    // Tính diện tích cửa
    return width * height;
  }

  @override
  List<String> get requiredParameters => ['width', 'height'];

  @override
  String get unit => 'm²';

  @override
  Material copyWith({double? pricePerUnit}) {
    // Tạo bản sao với giá mới hoặc giữ nguyên giá hiện tại
    return AluminumDoor(pricePerUnit: pricePerUnit ?? this.pricePerUnit);
  }
}
