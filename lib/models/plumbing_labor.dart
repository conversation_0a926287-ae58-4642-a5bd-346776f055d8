import 'material_model.dart';

/// Lớp nhân công thi công điện nước
class PlumbingLabor extends Material {
  /// Hàm khởi tạo
  PlumbingLabor({required super.pricePerUnit})
      : super(
          name: '<PERSON>hân công điện nước',
          type: MaterialType.plumbingLabor,
          measurementUnit: MeasurementUnit.squareMeter,
        );

  @override
  double calculateQuantity(Map<String, dynamic> parameters) {
    // Trích xuất các thông số
    final double area = parameters['area'] ?? 0.0;
    
    // Diện tích thi công điện nước
    return area;
  }

  @override
  List<String> get requiredParameters => ['area'];

  @override
  String get unit => 'm²';

  @override
  Material copyWith({double? pricePerUnit}) {
    // Tạo bản sao với giá mới hoặc giữ nguyên giá hiện tại
    return PlumbingLabor(pricePerUnit: pricePerUnit ?? this.pricePerUnit);
  }
}
