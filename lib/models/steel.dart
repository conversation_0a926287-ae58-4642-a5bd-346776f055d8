import 'material_model.dart';

/// Lớp vật liệu sắt thép
class Steel extends Material {
  /// Trọng lượng trung bình của thép (kg/m)
  static const double averageSteelWeight = 0.888; // kg/m cho thép Φ12

  /// Hàm khởi tạo
  Steel({required super.pricePerUnit})
      : super(
          name: 'Sắt thép',
          type: MaterialType.steel,
          measurementUnit: MeasurementUnit.ton,
        );

  @override
  double calculateQuantity(Map<String, dynamic> parameters) {
    // Trích xuất các thông số
    final double length = parameters['length'] ?? 0.0;
    final int quantity = parameters['quantity'] ?? 0;

    // Tính tổng chiều dài sắt thép
    final double totalLength = length * quantity;
    
    // Chuyển đổi từ mét sang tấn (1000 kg = 1 tấn)
    return (totalLength * averageSteelWeight) / 1000;
  }

  @override
  List<String> get requiredParameters => ['length', 'quantity'];

  @override
  String get unit => 'tấn';

  @override
  Material copyWith({double? pricePerUnit}) {
    // Tạo bản sao với giá mới hoặc giữ nguyên giá hiện tại
    return Steel(pricePerUnit: pricePerUnit ?? this.pricePerUnit);
  }
}
