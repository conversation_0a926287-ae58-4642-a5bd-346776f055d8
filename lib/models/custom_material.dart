import 'material_model.dart';

/// Lớp vật liệu tùy chỉnh
class CustomMaterial extends Material {
  /// Đơn vị đo lường tùy chỉnh
  final String customUnit;

  /// Kích thước của viên gạch theo mét (chỉ áp dụng khi measurementUnit là piece)
  double? pieceLength;
  double? pieceWidth;
  double? pieceHeight;

  /// Hàm khởi tạo
  CustomMaterial({
    required super.name,
    required super.pricePerUnit,
    required this.customUnit,
    required super.measurementUnit,
    this.pieceLength,
    this.pieceWidth,
    this.pieceHeight,
  }) : super(type: MaterialType.custom);

  @override
  double calculateQuantity(Map<String, dynamic> parameters) {
    switch (measurementUnit) {
      case MeasurementUnit.squareMeter:
        // Diện tích
        final double area = parameters['area'] ?? 0.0;
        return area;
      case MeasurementUnit.cubicMeter:
        // Thể tích
        final double volume = parameters['volume'] ?? 0.0;
        return volume;
      case MeasurementUnit.meter:
        // Chiều dài
        final double length = parameters['length'] ?? 0.0;
        return length;
      case MeasurementUnit.piece:
        // Nếu có kích thước viên và có thông số tường, tính theo thể tích
        if (pieceLength != null &&
            pieceWidth != null &&
            pieceHeight != null &&
            parameters.containsKey('length') &&
            parameters.containsKey('width') &&
            parameters.containsKey('height')) {
          return _calculateByDimensions(parameters);
        } else {
          // Nếu không, trả về số lượng
          final int quantity = parameters['quantity'] ?? 0;
          return quantity.toDouble();
        }
      case MeasurementUnit.set:
      case MeasurementUnit.package:
      case MeasurementUnit.kilogram:
      case MeasurementUnit.ton:
      case MeasurementUnit.custom:
        // Số lượng
        final int quantity = parameters['quantity'] ?? 0;
        return quantity.toDouble();
    }
  }

  /// Tính số lượng viên dựa trên kích thước
  double _calculateByDimensions(Map<String, dynamic> parameters) {
    // Trích xuất các thông số
    final double length = parameters['length'] ?? 0.0;
    final double width = parameters['width'] ?? 0.0;
    final double height = parameters['height'] ?? 0.0;

    // Tính thể tích tường
    final double wallVolume = length * width * height;

    // Tính thể tích viên
    final double pieceVolume =
        (pieceLength ?? 0.0) * (pieceWidth ?? 0.0) * (pieceHeight ?? 0.0);

    if (pieceVolume <= 0) {
      return 0.0;
    }

    // Tính số lượng viên cần thiết
    // Thêm 5% cho hao hụt
    return (wallVolume / pieceVolume) * 1.05;
  }

  @override
  List<String> get requiredParameters {
    switch (measurementUnit) {
      case MeasurementUnit.squareMeter:
        return ['area'];
      case MeasurementUnit.cubicMeter:
        return ['volume'];
      case MeasurementUnit.meter:
        return ['length'];
      case MeasurementUnit.piece:
        // Nếu có kích thước viên, yêu cầu thông số tường
        if (pieceLength != null && pieceWidth != null && pieceHeight != null) {
          return ['length', 'width', 'height'];
        } else {
          return ['quantity'];
        }
      case MeasurementUnit.set:
      case MeasurementUnit.package:
      case MeasurementUnit.kilogram:
      case MeasurementUnit.ton:
      case MeasurementUnit.custom:
        return ['quantity'];
    }
  }

  @override
  String get unit => customUnit;

  @override
  Material copyWith({
    double? pricePerUnit,
    double? pieceLength,
    double? pieceWidth,
    double? pieceHeight,
  }) {
    // Tạo bản sao với giá mới hoặc giữ nguyên giá hiện tại
    return CustomMaterial(
      name: name,
      pricePerUnit: pricePerUnit ?? this.pricePerUnit,
      customUnit: customUnit,
      measurementUnit: measurementUnit,
      pieceLength: pieceLength ?? this.pieceLength,
      pieceWidth: pieceWidth ?? this.pieceWidth,
      pieceHeight: pieceHeight ?? this.pieceHeight,
    );
  }
}
