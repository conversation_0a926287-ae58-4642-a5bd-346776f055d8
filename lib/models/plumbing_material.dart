import 'material_model.dart';

/// Lớp vật tư điện nước
class PlumbingMaterial extends Material {
  /// Hàm khởi tạo
  PlumbingMaterial({required super.pricePerUnit})
    : super(
        name: 'Vật tư điện nước',
        type: MaterialType.plumbingMaterial,
        measurementUnit: MeasurementUnit.squareMeter,
      );

  @override
  double calculateQuantity(Map<String, dynamic> parameters) {
    // Trích xuất các thông số
    final double area = parameters['area'] ?? 0.0;

    // Diện tích thi công điện nước (dựa trên tổng diện tích sàn)
    return area;
  }

  @override
  List<String> get requiredParameters => ['area'];

  @override
  String get unit => 'm²';

  @override
  Material copyWith({double? pricePerUnit}) {
    // Tạo bản sao với giá mới hoặc giữ nguyên giá hiện tại
    return PlumbingMaterial(pricePerUnit: pricePerUnit ?? this.pricePerUnit);
  }
}
