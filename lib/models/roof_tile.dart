import 'material_model.dart';

/// Lớp vật liệu ngói tây
class RoofTile extends Material {
  /// Hàm khởi tạo
  RoofTile({required super.pricePerUnit})
      : super(
          name: '<PERSON><PERSON><PERSON>',
          type: MaterialType.roofTile,
          measurementUnit: MeasurementUnit.squareMeter,
        );

  @override
  double calculateQuantity(Map<String, dynamic> parameters) {
    // Trích xuất các thông số
    final double area = parameters['area'] ?? 0.0;
    
    // Diện tích mái ngói bằng diện tích đã nhập
    return area;
  }

  @override
  List<String> get requiredParameters => ['area'];

  @override
  String get unit => 'm²';

  @override
  Material copyWith({double? pricePerUnit}) {
    // Tạo bản sao với giá mới hoặc giữ nguyên giá hiện tại
    return RoofTile(pricePerUnit: pricePerUnit ?? this.pricePerUnit);
  }
}
