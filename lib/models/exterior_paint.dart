import 'material_model.dart';

/// Lớp vật liệu sơn ngoại thất
class ExteriorPaint extends Material {
  /// Đ<PERSON><PERSON> mức sơn trên mỗi m² (lít)
  static const double paintPerSquareMeter = 0.2;

  /// Hàm khởi tạo
  ExteriorPaint({required super.pricePerUnit})
      : super(
          name: 'Sơn ngoại thất',
          type: MaterialType.exteriorPaint,
          measurementUnit: MeasurementUnit.squareMeter,
        );

  @override
  double calculateQuantity(Map<String, dynamic> parameters) {
    // Trích xuất các thông số
    final double area = parameters['area'] ?? 0.0;
    
    // Diện tích sơn bằng diện tích đã nhập
    return area;
  }

  @override
  List<String> get requiredParameters => ['area'];

  @override
  String get unit => 'm²';

  @override
  Material copyWith({double? pricePerUnit}) {
    // Tạo bản sao với giá mới hoặc giữ nguyên gi<PERSON> hiện tại
    return ExteriorPaint(pricePerUnit: pricePerUnit ?? this.pricePerUnit);
  }
}
