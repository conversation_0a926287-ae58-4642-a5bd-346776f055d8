import 'package:flutter/foundation.dart';
import 'material_model.dart' as model;
import 'brick.dart';
import 'sand.dart';
import 'plastering_sand.dart';
import 'cement.dart';
import 'steel.dart';
import 'stone.dart';
import 'tile6060.dart';
import 'roof_tile.dart';
import 'metal_sheet.dart';
import 'insulated_metal.dart';
import 'gypsum.dart';
import 'exterior_paint.dart';
import 'interior_paint.dart';
import 'aluminum_door.dart';
import 'composite_door.dart';
import 'labor.dart';
import 'plumbing_labor.dart';
import 'plumbing_material.dart';
import 'concrete_sand.dart';
import 'custom_material.dart';

/// Lớp quản lý tất cả các vật liệu xây dựng
class MaterialProvider extends ChangeNotifier {
  /// Danh sách tất cả các vật liệu có sẵn
  List<model.Material> _materials = [];

  /// Vật liệu đang được chọn
  model.Material? _selectedMaterial;

  /// Hàm khởi tạo
  MaterialProvider() {
    // Khởi tạo với giá mặc định
    _materials = [
      // Vật liệu cơ bản
      Brick(pricePerUnit: 1500),
      Sand(pricePerUnit: 300000),
      PlasteringSand(pricePerUnit: 320000),
      ConcreteSand(pricePerUnit: 450000), // Cát bê tông
      Cement(pricePerUnit: 1800000), // Giá theo tấn
      Steel(pricePerUnit: 18000000), // Giá theo tấn
      Stone(pricePerUnit: 350000),
      Tile6060(pricePerUnit: 150000),

      // Vật liệu mới
      RoofTile(pricePerUnit: 180000),
      MetalSheet(pricePerUnit: 120000),
      InsulatedMetal(pricePerUnit: 220000),
      Gypsum(pricePerUnit: 160000),
      ExteriorPaint(pricePerUnit: 80000),
      InteriorPaint(pricePerUnit: 65000),
      AluminumDoor(pricePerUnit: 1800000),
      CompositeDoor(pricePerUnit: 3500000),
      Labor(pricePerUnit: 1350000),
      PlumbingLabor(pricePerUnit: 120000),
      PlumbingMaterial(pricePerUnit: 280000),
    ];

    // Đặt vật liệu mặc định được chọn
    _selectedMaterial = _materials.first;
  }

  /// Lấy tất cả vật liệu
  List<model.Material> get materials => _materials;

  /// Lấy vật liệu đang được chọn
  model.Material? get selectedMaterial => _selectedMaterial;

  /// Đặt vật liệu được chọn
  void selectMaterial(model.Material material) {
    _selectedMaterial = material;
    notifyListeners();
  }

  /// Cập nhật giá vật liệu
  void updateMaterialPrice(String materialName, double newPrice) {
    final index = _materials.indexWhere((m) => m.name == materialName);
    if (index != -1) {
      _materials[index].pricePerUnit = newPrice;

      // Nếu vật liệu được chọn đã được cập nhật, cập nhật nó luôn
      if (_selectedMaterial?.name == materialName) {
        _selectedMaterial = _materials[index];
      }

      notifyListeners();
    }
  }

  /// Cập nhật kích thước gạch
  void updateBrickDimensions(double length, double width, double height) {
    final index = _materials.indexWhere((m) => m.name == 'Gạch xây');
    if (index != -1 && _materials[index] is Brick) {
      final brick = _materials[index] as Brick;
      _materials[index] =
          brick.copyWith(
                brickLength: length,
                brickWidth: width,
                brickHeight: height,
              )
              as Brick;

      // Nếu vật liệu được chọn là gạch, cập nhật nó luôn
      if (_selectedMaterial?.name == 'Gạch xây') {
        _selectedMaterial = _materials[index];
      }

      notifyListeners();
    }
  }

  /// Lấy kích thước gạch hiện tại
  Map<String, double> getBrickDimensions() {
    final index = _materials.indexWhere((m) => m.name == 'Gạch xây');
    if (index != -1 && _materials[index] is Brick) {
      final brick = _materials[index] as Brick;
      return {
        'length': brick.brickLength,
        'width': brick.brickWidth,
        'height': brick.brickHeight,
      };
    }

    // Trả về kích thước mặc định nếu không tìm thấy gạch
    return {'length': 0.2, 'width': 0.1, 'height': 0.05};
  }

  /// Cập nhật tất cả giá vật liệu từ một map
  void updateAllPrices(Map<String, double> priceMap) {
    for (final entry in priceMap.entries) {
      updateMaterialPrice(entry.key, entry.value);
    }
  }

  /// Thêm vật liệu tùy chỉnh mới
  void addCustomMaterial({
    required String name,
    required double pricePerUnit,
    required String customUnit,
    required model.MeasurementUnit measurementUnit,
    double? pieceLength,
    double? pieceWidth,
    double? pieceHeight,
  }) {
    final newMaterial = CustomMaterial(
      name: name,
      pricePerUnit: pricePerUnit,
      customUnit: customUnit,
      measurementUnit: measurementUnit,
      pieceLength: pieceLength,
      pieceWidth: pieceWidth,
      pieceHeight: pieceHeight,
    );

    _materials.add(newMaterial);
    notifyListeners();
  }

  /// Cập nhật kích thước viên cho vật liệu tùy chỉnh
  void updateCustomMaterialDimensions(
    String materialName,
    double length,
    double width,
    double height,
  ) {
    final index = _materials.indexWhere(
      (m) => m.name == materialName && m is CustomMaterial,
    );
    if (index != -1 && _materials[index] is CustomMaterial) {
      final customMaterial = _materials[index] as CustomMaterial;
      _materials[index] =
          customMaterial.copyWith(
                pieceLength: length,
                pieceWidth: width,
                pieceHeight: height,
              )
              as CustomMaterial;

      // Nếu vật liệu được chọn là vật liệu tùy chỉnh này, cập nhật nó luôn
      if (_selectedMaterial?.name == materialName) {
        _selectedMaterial = _materials[index];
      }

      notifyListeners();
    }
  }

  /// Lấy kích thước viên của vật liệu tùy chỉnh
  Map<String, double?> getCustomMaterialDimensions(String materialName) {
    final index = _materials.indexWhere(
      (m) => m.name == materialName && m is CustomMaterial,
    );
    if (index != -1 && _materials[index] is CustomMaterial) {
      final customMaterial = _materials[index] as CustomMaterial;
      return {
        'length': customMaterial.pieceLength,
        'width': customMaterial.pieceWidth,
        'height': customMaterial.pieceHeight,
      };
    }

    // Trả về null nếu không tìm thấy vật liệu
    return {'length': null, 'width': null, 'height': null};
  }

  /// Xóa vật liệu
  void removeMaterial(model.Material material) {
    // Chỉ cho phép xóa vật liệu tùy chỉnh
    if (material.type == model.MaterialType.custom) {
      _materials.remove(material);

      // Nếu vật liệu đang được chọn bị xóa, chọn vật liệu đầu tiên
      if (_selectedMaterial == material) {
        _selectedMaterial = _materials.first;
      }

      notifyListeners();
    }
  }
}
