import 'material_model.dart';

/// Lớp nhân công thi công xây dựng
class Labor extends Material {
  /// Hàm khởi tạo
  Labor({required super.pricePerUnit})
    : super(
        name: 'Nhân công xây dựng',
        type: MaterialType.labor,
        measurementUnit: MeasurementUnit.squareMeter,
      );

  @override
  double calculateQuantity(Map<String, dynamic> parameters) {
    // Trích xuất các thông số
    final double area = parameters['area'] ?? 0.0;

    // Diện tích xây dựng
    return area;
  }

  @override
  List<String> get requiredParameters => ['area'];

  @override
  String get unit => 'm²';

  @override
  Material copyWith({double? pricePerUnit}) {
    // Tạo bản sao với giá mới hoặc giữ nguyên giá hiện tại
    return Labor(pricePerUnit: pricePerUnit ?? this.pricePerUnit);
  }
}
