import 'material_model.dart';

/// Lớp vật liệu tôn xốp
class InsulatedMetal extends Material {
  /// Hàm khởi tạo
  InsulatedMetal({required super.pricePerUnit})
      : super(
          name: 'Tôn xốp',
          type: MaterialType.insulatedMetal,
          measurementUnit: MeasurementUnit.squareMeter,
        );

  @override
  double calculateQuantity(Map<String, dynamic> parameters) {
    // Trích xuất các thông số
    final double area = parameters['area'] ?? 0.0;
    
    // Diện tích tôn xốp bằng diện tích đã nhập
    // Thêm 5% cho phần chồng mí và hao hụt
    return area * 1.05;
  }

  @override
  List<String> get requiredParameters => ['area'];

  @override
  String get unit => 'm²';

  @override
  Material copyWith({double? pricePerUnit}) {
    // Tạo bản sao với giá mới hoặc giữ nguyên giá hiện tại
    return InsulatedMetal(pricePerUnit: pricePerUnit ?? this.pricePerUnit);
  }
}
