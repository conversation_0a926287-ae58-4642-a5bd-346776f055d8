import 'material_model.dart';

/// Lớp vật liệu cát bê tông
class ConcreteSand extends Material {
  /// Hàm khởi tạo
  ConcreteSand({required super.pricePerUnit})
      : super(
          name: 'C<PERSON><PERSON> bê tông',
          type: MaterialType.concreteSand,
          measurementUnit: MeasurementUnit.cubicMeter,
        );

  @override
  double calculateQuantity(Map<String, dynamic> parameters) {
    // Trích xuất các thông số
    final double volume = parameters['volume'] ?? 0.0;

    // Tính lượng cát bê tông (0.7 m³ cát trên mỗi m³ bê tông)
    // Tỷ lệ cát trong bê tông thường là 70%
    return volume * 0.7;
  }

  @override
  List<String> get requiredParameters => ['volume'];

  @override
  String get unit => 'm³';

  @override
  Material copyWith({double? pricePerUnit}) {
    // Tạo bản sao với giá mới hoặc giữ nguyên giá hiện tại
    return ConcreteSand(pricePerUnit: pricePerUnit ?? this.pricePerUnit);
  }
}
