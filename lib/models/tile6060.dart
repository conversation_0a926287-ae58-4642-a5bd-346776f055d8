import 'material_model.dart';

/// Lớp vật liệu gạch ốp lát
class Tile6060 extends Material {
  /// Diện tích chuẩn của viên gạch theo mét vuông
  static const double standardTileArea = 0.36; // 0.6m x 0.6m

  /// Hàm khởi tạo
  Tile6060({required super.pricePerUnit})
      : super(
          name: 'Gạch lát nền 60x60',
          type: MaterialType.tile,
          measurementUnit: MeasurementUnit.squareMeter,
        );

  @override
  double calculateQuantity(Map<String, dynamic> parameters) {
    // Trích xuất các thông số
    final double surfaceArea = parameters['surfaceArea'] ?? 0.0;

    // Tính số lượng gạch cần thiết
    // Thêm 10% cho hao hụt và cắt
    return (surfaceArea / standardTileArea) * 1.1;
  }

  @override
  List<String> get requiredParameters => ['surfaceArea'];

  @override
  String get unit => 'm²';

  @override
  Material copyWith({double? pricePerUnit}) {
    // Tạo bản sao với giá mới hoặc giữ nguyên giá hiện tại
    return Tile6060(pricePerUnit: pricePerUnit ?? this.pricePerUnit);
  }
}
