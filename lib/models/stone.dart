import 'material_model.dart';

/// Lớp vật liệu đá
class Stone extends Material {
  /// Hàm khởi tạo
  Stone({required super.pricePerUnit})
      : super(
          name: 'Đá',
          type: MaterialType.stone,
          measurementUnit: MeasurementUnit.cubicMeter,
        );

  @override
  double calculateQuantity(Map<String, dynamic> parameters) {
    // Trích xuất các thông số
    final double volume = parameters['volume'] ?? 0.0;

    // Tính lượng đá (1.0 m³ đá trên mỗi m³)
    return volume * 1.0;
  }

  @override
  List<String> get requiredParameters => ['volume'];

  @override
  String get unit => 'm³';

  @override
  Material copyWith({double? pricePerUnit}) {
    // Tạo bản sao với giá mới hoặc giữ nguyên giá hiện tại
    return Stone(pricePerUnit: pricePerUnit ?? this.pricePerUnit);
  }
}
