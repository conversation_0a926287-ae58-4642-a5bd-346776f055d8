import 'package:flutter/foundation.dart';
import 'project_model.dart';
import 'building_model.dart';
import '../../services/database/project_database.dart';

/// Provider quản lý danh sách dự án
class ProjectProvider extends ChangeNotifier {
  /// Danh sách dự án
  List<Project> _projects = [];

  /// Dự án đang được chọn
  Project? _selectedProject;

  /// Dự án đang được tạo (trong wizard)
  Project? _draftProject;

  /// Database service
  final ProjectDatabase _database = ProjectDatabase();

  /// Constructor
  ProjectProvider() {
    _loadProjects();
  }

  /// L<PERSON>y danh sách dự án
  List<Project> get projects => _projects;

  /// Lấy dự án đang được chọn
  Project? get selectedProject => _selectedProject;

  /// Lấy dự án đang được tạo
  Project? get draftProject => _draftProject;

  /// Tải danh sách dự án từ database
  Future<void> _loadProjects() async {
    try {
      _projects = await _database.getAllProjects();
      notifyListeners();
    } catch (e) {
      debugPrint('Lỗi khi tải danh sách dự án: $e');
    }
  }

  /// Thêm dự án mới
  Future<void> addProject(Project project) async {
    try {
      final id = await _database.insertProject(project);
      final newProject = Project(
        id: id,
        name: project.name,
        location: project.location,
        imagePath: project.imagePath,
        floors: project.floors,
        roof: project.roof,
        foundationType: project.foundationType,
        structureType: project.structureType,
        selectedMaterialIds: project.selectedMaterialIds,
        detailedParameters: project.detailedParameters,
        results: project.results,
      );

      _projects.add(newProject);
      _selectedProject = newProject;
      _draftProject = null; // Xóa dự án nháp sau khi lưu
      notifyListeners();
    } catch (e) {
      debugPrint('Lỗi khi thêm dự án: $e');
    }
  }

  /// Cập nhật dự án
  Future<void> updateProject(Project project) async {
    try {
      await _database.updateProject(project);

      final index = _projects.indexWhere((p) => p.id == project.id);
      if (index != -1) {
        _projects[index] = project;

        // Cập nhật dự án đang chọn nếu cần
        if (_selectedProject?.id == project.id) {
          _selectedProject = project;
        }

        notifyListeners();
      }
    } catch (e) {
      debugPrint('Lỗi khi cập nhật dự án: $e');
    }
  }

  /// Xóa dự án
  Future<void> deleteProject(String projectId) async {
    try {
      await _database.deleteProject(projectId);

      _projects.removeWhere((p) => p.id == projectId);

      // Xóa dự án đang chọn nếu cần
      if (_selectedProject?.id == projectId) {
        _selectedProject = _projects.isNotEmpty ? _projects.first : null;
      }

      notifyListeners();
    } catch (e) {
      debugPrint('Lỗi khi xóa dự án: $e');
    }
  }

  /// Chọn dự án
  void selectProject(Project project) {
    _selectedProject = project;
    notifyListeners();
  }

  /// Sao chép dự án
  Future<void> duplicateProject(Project project) async {
    try {
      final newProject = project.copyWith(name: '${project.name} (Bản sao)');

      await addProject(newProject);
    } catch (e) {
      debugPrint('Lỗi khi sao chép dự án: $e');
    }
  }

  /// Đổi tên dự án
  Future<void> renameProject(String projectId, String newName) async {
    try {
      final index = _projects.indexWhere((p) => p.id == projectId);
      if (index != -1) {
        final project = _projects[index];
        project.name = newName;
        project.updatedAt = DateTime.now();

        await updateProject(project);
      }
    } catch (e) {
      debugPrint('Lỗi khi đổi tên dự án: $e');
    }
  }

  /// Tạo dự án nháp mới
  void createDraftProject(String name, String location, String? imagePath) {
    _draftProject = Project(
      name: name,
      location: location,
      imagePath: imagePath,
    );
    notifyListeners();
  }

  /// Cập nhật thông tin cơ bản của dự án nháp
  void updateDraftBasicInfo(String name, String location, String? imagePath) {
    if (_draftProject != null) {
      _draftProject!.name = name;
      _draftProject!.location = location;
      _draftProject!.imagePath = imagePath;
      notifyListeners();
    } else {
      createDraftProject(name, location, imagePath);
    }
  }

  /// Cập nhật danh sách tầng của dự án nháp
  void updateDraftFloors(List<Floor> floors) {
    if (_draftProject != null) {
      _draftProject!.floors = floors;
      notifyListeners();
    }
  }

  /// Cập nhật thông tin mái của dự án nháp
  void updateDraftRoof(Roof? roof) {
    if (_draftProject != null) {
      _draftProject!.roof = roof;
      notifyListeners();
    }
  }

  /// Cập nhật loại móng của dự án nháp
  void updateDraftFoundationType(FoundationType? foundationType) {
    if (_draftProject != null) {
      _draftProject!.foundationType = foundationType;
      notifyListeners();
    }
  }

  /// Cập nhật loại kết cấu của dự án nháp
  void updateDraftStructureType(StructureType? structureType) {
    if (_draftProject != null) {
      _draftProject!.structureType = structureType;
      notifyListeners();
    }
  }

  /// Cập nhật danh sách vật liệu của dự án nháp
  void updateDraftMaterials(List<String> materialIds) {
    if (_draftProject != null) {
      _draftProject!.selectedMaterialIds = materialIds;
      notifyListeners();
    }
  }

  /// Cập nhật thông số chi tiết của dự án nháp
  void updateDraftDetailedParameters(DetailedParameters parameters) {
    if (_draftProject != null) {
      _draftProject!.detailedParameters = parameters;
      notifyListeners();
    }
  }

  /// Cập nhật kết quả tính toán của dự án nháp
  void updateDraftResults(Map<String, dynamic> results) {
    if (_draftProject != null) {
      _draftProject!.results = results;
      notifyListeners();
    }
  }

  /// Xóa dự án nháp
  void clearDraftProject() {
    _draftProject = null;
    notifyListeners();
  }

  /// Lưu dự án nháp
  Future<void> saveDraftProject() async {
    if (_draftProject != null) {
      await addProject(_draftProject!);
    }
  }
}
