/// Loại mái
enum RoofType {
  flat,     // Mái bằng
  metal,    // <PERSON><PERSON>i tôn
  tile,     // M<PERSON>i ngói
}

/// Loại móng
enum FoundationType {
  strip,    // Móng băng
  isolated, // Móng đơn
  pile,     // <PERSON>óng cọc
  raft,     // Móng bè
}

/// Model tầng nhà
class Floor {
  /// Số thứ tự tầng (1, 2, 3, ...)
  final int number;
  
  /// Diện tích tầng (m²)
  double area;
  
  /// Chiều cao tầng (m)
  double height;
  
  /// Constructor
  Floor({
    required this.number,
    required this.area,
    required this.height,
  });
  
  /// Tạo bản sao của tầng
  Floor copyWith({
    int? number,
    double? area,
    double? height,
  }) {
    return Floor(
      number: number ?? this.number,
      area: area ?? this.area,
      height: height ?? this.height,
    );
  }
  
  /// Chuyển đổi từ Map sang Floor
  factory Floor.fromMap(Map<String, dynamic> map) {
    return Floor(
      number: map['number'],
      area: map['area'],
      height: map['height'],
    );
  }
  
  /// <PERSON>yển đổi từ Floor sang Map
  Map<String, dynamic> toMap() {
    return {
      'number': number,
      'area': area,
      'height': height,
    };
  }
}

/// Model mái nhà
class Roof {
  /// Loại mái
  RoofType type;
  
  /// Diện tích mái (m²)
  double area;
  
  /// Constructor
  Roof({
    required this.type,
    required this.area,
  });
  
  /// Tạo bản sao của mái
  Roof copyWith({
    RoofType? type,
    double? area,
  }) {
    return Roof(
      type: type ?? this.type,
      area: area ?? this.area,
    );
  }
  
  /// Chuyển đổi từ Map sang Roof
  factory Roof.fromMap(Map<String, dynamic> map) {
    return Roof(
      type: RoofType.values[map['type']],
      area: map['area'],
    );
  }
  
  /// Chuyển đổi từ Roof sang Map
  Map<String, dynamic> toMap() {
    return {
      'type': type.index,
      'area': area,
    };
  }
}

/// Model thông số chi tiết
class DetailedParameters {
  /// Thông số móng
  Map<String, dynamic> foundation;
  
  /// Thông số tường
  Map<String, dynamic> walls;
  
  /// Thông số cửa
  Map<String, dynamic> doors;
  
  /// Thông số khác
  Map<String, dynamic> others;
  
  /// Constructor
  DetailedParameters({
    Map<String, dynamic>? foundation,
    Map<String, dynamic>? walls,
    Map<String, dynamic>? doors,
    Map<String, dynamic>? others,
  }) : 
    foundation = foundation ?? {},
    walls = walls ?? {},
    doors = doors ?? {},
    others = others ?? {};
  
  /// Tạo bản sao của thông số chi tiết
  DetailedParameters copyWith({
    Map<String, dynamic>? foundation,
    Map<String, dynamic>? walls,
    Map<String, dynamic>? doors,
    Map<String, dynamic>? others,
  }) {
    return DetailedParameters(
      foundation: foundation ?? Map.from(this.foundation),
      walls: walls ?? Map.from(this.walls),
      doors: doors ?? Map.from(this.doors),
      others: others ?? Map.from(this.others),
    );
  }
  
  /// Chuyển đổi từ Map sang DetailedParameters
  factory DetailedParameters.fromMap(Map<String, dynamic> map) {
    return DetailedParameters(
      foundation: Map<String, dynamic>.from(map['foundation'] ?? {}),
      walls: Map<String, dynamic>.from(map['walls'] ?? {}),
      doors: Map<String, dynamic>.from(map['doors'] ?? {}),
      others: Map<String, dynamic>.from(map['others'] ?? {}),
    );
  }
  
  /// Chuyển đổi từ DetailedParameters sang Map
  Map<String, dynamic> toMap() {
    return {
      'foundation': foundation,
      'walls': walls,
      'doors': doors,
      'others': others,
    };
  }
}
