import 'package:uuid/uuid.dart';
import 'building_model.dart';

/// Loại kết cấu
enum StructureType {
  concrete, // Bê tông cốt thép
  steel, // Khung thép
  brick, // Gạch
  wood, // Gỗ
}

/// Model dự án xây dựng
class Project {
  /// ID duy nhất của dự án
  final String id;

  /// Tên dự án
  String name;

  /// Địa điểm dự án
  String location;

  /// Đường dẫn đến ảnh đại diện (nếu có)
  String? imagePath;

  /// Ngày tạo dự án
  final DateTime createdAt;

  /// Ngày cập nhật dự án
  DateTime updatedAt;

  /// Danh sách các tầng
  List<Floor> floors;

  /// Thông tin về mái
  Roof? roof;

  /// Loại móng được chọn
  FoundationType? foundationType;

  /// Loại kết cấu được chọn
  StructureType? structureType;

  /// Danh sách ID vật liệu được chọn
  List<String> selectedMaterialIds;

  /// Thông số chi tiết của dự án
  DetailedParameters detailedParameters;

  /// Kết quả tính toán
  Map<String, dynamic> results;

  /// Constructor
  Project({
    String? id,
    required this.name,
    required this.location,
    this.imagePath,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<Floor>? floors,
    this.roof,
    this.foundationType,
    this.structureType,
    List<String>? selectedMaterialIds,
    DetailedParameters? detailedParameters,
    Map<String, dynamic>? results,
  }) : id = id ?? const Uuid().v4(),
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now(),
       floors = floors ?? [Floor(number: 1, area: 0, height: 0)],
       selectedMaterialIds = selectedMaterialIds ?? [],
       detailedParameters = detailedParameters ?? DetailedParameters(),
       results = results ?? {};

  /// Tạo bản sao của dự án
  Project copyWith({
    String? name,
    String? location,
    String? imagePath,
    List<Floor>? floors,
    Roof? roof,
    FoundationType? foundationType,
    StructureType? structureType,
    List<String>? selectedMaterialIds,
    DetailedParameters? detailedParameters,
    Map<String, dynamic>? results,
  }) {
    return Project(
      id: const Uuid().v4(), // Tạo ID mới cho bản sao
      name: name ?? this.name,
      location: location ?? this.location,
      imagePath: imagePath ?? this.imagePath,
      createdAt: DateTime.now(), // Thời gian tạo mới
      floors: floors ?? List.from(this.floors),
      roof: roof ?? this.roof,
      foundationType: foundationType ?? this.foundationType,
      structureType: structureType ?? this.structureType,
      selectedMaterialIds:
          selectedMaterialIds ?? List.from(this.selectedMaterialIds),
      detailedParameters:
          detailedParameters ?? this.detailedParameters.copyWith(),
      results: results ?? Map.from(this.results),
    );
  }

  /// Chuyển đổi từ Map sang Project
  factory Project.fromMap(Map<String, dynamic> map) {
    List<Floor> floors = [];
    if (map['floors'] != null) {
      final floorsList = List<Map<String, dynamic>>.from(map['floors']);
      floors = floorsList.map((floorMap) => Floor.fromMap(floorMap)).toList();
    } else {
      floors = [Floor(number: 1, area: 0, height: 0)];
    }

    Roof? roof;
    if (map['roof'] != null) {
      roof = Roof.fromMap(Map<String, dynamic>.from(map['roof']));
    }

    return Project(
      id: map['id'],
      name: map['name'],
      location: map['location'],
      imagePath: map['imagePath'],
      createdAt: DateTime.parse(map['createdAt']),
      updatedAt: DateTime.parse(map['updatedAt']),
      floors: floors,
      roof: roof,
      foundationType:
          map['foundationType'] != null
              ? FoundationType.values[map['foundationType']]
              : null,
      structureType:
          map['structureType'] != null
              ? StructureType.values[map['structureType']]
              : null,
      selectedMaterialIds: List<String>.from(map['selectedMaterialIds'] ?? []),
      detailedParameters:
          map['detailedParameters'] != null
              ? DetailedParameters.fromMap(
                Map<String, dynamic>.from(map['detailedParameters']),
              )
              : DetailedParameters(),
      results: Map<String, dynamic>.from(map['results'] ?? {}),
    );
  }

  /// Chuyển đổi từ Project sang Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'location': location,
      'imagePath': imagePath,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'floors': floors.map((floor) => floor.toMap()).toList(),
      'roof': roof?.toMap(),
      'foundationType': foundationType?.index,
      'structureType': structureType?.index,
      'selectedMaterialIds': selectedMaterialIds,
      'detailedParameters': detailedParameters.toMap(),
      'results': results,
    };
  }
}
