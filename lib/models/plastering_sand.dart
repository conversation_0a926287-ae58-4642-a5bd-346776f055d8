import 'material_model.dart';
import 'brick.dart';

/// Lớp vật liệu cát trát
class PlasteringSand extends Material {
  /// Hàm khởi tạo
  PlasteringSand({required super.pricePerUnit})
      : super(
          name: 'C<PERSON>t trát',
          type: MaterialType.sand,
          measurementUnit: MeasurementUnit.cubicMeter,
        );

  @override
  double calculateQuantity(Map<String, dynamic> parameters) {
    // Kiểm tra xem có thông số loại tường không
    if (parameters.containsKey('wallType')) {
      return _calculateByWallType(parameters);
    } else {
      return _calculateByArea(parameters);
    }
  }

  /// Tính lượng cát trát dựa trên diện tích
  double _calculateByArea(Map<String, dynamic> parameters) {
    // Trích xuất các thông số
    final double area = parameters['area'] ?? 0.0;

    // Tính lượng cát trát (0.002 m³ cát trên mỗi m² diện tích trát)
    return area * 0.002;
  }

  /// T<PERSON>h lượng cát trát dựa trên loại tường
  double _calculateByWallType(Map<String, dynamic> parameters) {
    // Trích xuất các thông số
    final WallType wallType = parameters['wallType'] as WallType;
    final double length = parameters['length'] ?? 0.0;
    final double height = parameters['height'] ?? 0.0;

    // Tính diện tích tường
    final double wallArea = length * height;

    // Lượng cát trát trên mỗi m² tường (m³)
    double sandPerSquareMeter;

    switch (wallType) {
      case WallType.wall10cm1Side:
        sandPerSquareMeter = 0.002; // Tường 10cm chát 1 mặt
        break;
      case WallType.wall10cm2Side:
        sandPerSquareMeter = 0.004; // Tường 10cm chát 2 mặt (gấp đôi)
        break;
      case WallType.wall20cm1Side:
        sandPerSquareMeter = 0.002; // Tường 20cm chát 1 mặt
        break;
      case WallType.wall20cm2Side:
        sandPerSquareMeter = 0.004; // Tường 20cm chát 2 mặt (gấp đôi)
        break;
    }

    // Tính lượng cát trát cần thiết
    return wallArea * sandPerSquareMeter;
  }

  @override
  List<String> get requiredParameters => ['area'];

  @override
  String get unit => 'm³';

  @override
  Material copyWith({double? pricePerUnit}) {
    // Tạo bản sao với giá mới hoặc giữ nguyên giá hiện tại
    return PlasteringSand(pricePerUnit: pricePerUnit ?? this.pricePerUnit);
  }
}
