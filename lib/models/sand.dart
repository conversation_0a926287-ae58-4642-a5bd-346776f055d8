import 'material_model.dart';
import 'brick.dart';

/// Lớp vật liệu cát xây
class Sand extends Material {
  /// Hàm khởi tạo
  Sand({required super.pricePerUnit})
      : super(
          name: '<PERSON><PERSON><PERSON> xây',
          type: MaterialType.sand,
          measurementUnit: MeasurementUnit.cubicMeter,
        );

  @override
  double calculateQuantity(Map<String, dynamic> parameters) {
    // Kiểm tra xem có thông số loại tường không
    if (parameters.containsKey('wallType')) {
      return _calculateByWallType(parameters);
    } else {
      return _calculateByVolume(parameters);
    }
  }

  /// Tính lượng cát dựa trên thể tích
  double _calculateByVolume(Map<String, dynamic> parameters) {
    // Trích xuất các thông số
    final double wallVolume = parameters['wallVolume'] ?? 0.0;

    // Tính lượng cát (0.02 m³ cát trên mỗi m³ tường)
    return wallVolume * 0.02;
  }

  /// Tính lượng cát dựa trên loại tường
  double _calculateByWallType(Map<String, dynamic> parameters) {
    // Trích xuất các thông số
    final WallType wallType = parameters['wallType'] as WallType;
    final double length = parameters['length'] ?? 0.0;
    final double height = parameters['height'] ?? 0.0;

    // Tính diện tích tường
    final double wallArea = length * height;

    // Lượng cát trên mỗi m² tường (m³)
    double sandPerSquareMeter;

    switch (wallType) {
      case WallType.wall10cm1Side:
        sandPerSquareMeter = 0.002; // Tường 10cm chát 1 mặt
        break;
      case WallType.wall10cm2Side:
        sandPerSquareMeter = 0.003; // Tường 10cm chát 2 mặt (thêm cát cho chát)
        break;
      case WallType.wall20cm1Side:
        sandPerSquareMeter = 0.004; // Tường 20cm chát 1 mặt
        break;
      case WallType.wall20cm2Side:
        sandPerSquareMeter = 0.005; // Tường 20cm chát 2 mặt (thêm cát cho chát)
        break;
    }

    // Tính lượng cát cần thiết
    return wallArea * sandPerSquareMeter;
  }

  @override
  List<String> get requiredParameters => ['wallVolume'];

  @override
  String get unit => 'm³';

  @override
  Material copyWith({double? pricePerUnit}) {
    // Tạo bản sao với giá mới hoặc giữ nguyên giá hiện tại
    return Sand(pricePerUnit: pricePerUnit ?? this.pricePerUnit);
  }
}
