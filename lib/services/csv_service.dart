import 'dart:io';
import 'package:csv/csv.dart';
import 'package:file_picker/file_picker.dart';
import 'package:path_provider/path_provider.dart';
import '../models/material_model.dart' as model;

/// Service quản lý import/export CSV
class CsvService {
  /// Xuất danh sách vật liệu ra file CSV
  Future<String?> exportMaterialsToCSV(List<model.Material> materials) async {
    try {
      // Tạo dữ liệu CSV
      final List<List<dynamic>> rows = [];

      // Thêm header
      rows.add(['Tên', 'Loại', 'Đơn vị', 'Gi<PERSON>']);

      // Thêm dữ liệu
      for (final material in materials) {
        rows.add([
          material.name,
          _getMaterialTypeName(material.type),
          material.unit,
          material.pricePerUnit,
        ]);
      }

      // Chuyển đổi thành chuỗi CSV
      final csvData = const ListToCsvConverter().convert(rows);

      // Lưu vào file
      final directory = await getApplicationDocumentsDirectory();
      final path =
          '${directory.path}/materials_${DateTime.now().millisecondsSinceEpoch}.csv';
      final file = File(path);
      await file.writeAsString(csvData);

      return path;
    } catch (e) {
      print('Lỗi khi xuất CSV: $e');
      return null;
    }
  }

  /// Nhập danh sách vật liệu từ file CSV
  Future<List<Map<String, dynamic>>?> importMaterialsFromCSV() async {
    try {
      // Chọn file
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['csv'],
      );

      if (result == null || result.files.isEmpty) {
        return null;
      }

      final file = File(result.files.first.path!);
      final csvString = await file.readAsString();

      // Phân tích CSV
      final List<List<dynamic>> rowsAsListOfValues = const CsvToListConverter()
          .convert(csvString);

      // Kiểm tra header
      if (rowsAsListOfValues.isEmpty) {
        return null;
      }

      final header = rowsAsListOfValues.first;
      if (header.length < 4 ||
          header[0].toString().toLowerCase() != 'tên' ||
          header[2].toString().toLowerCase() != 'đơn vị' ||
          header[3].toString().toLowerCase() != 'giá') {
        throw Exception('File CSV không đúng định dạng');
      }

      // Chuyển đổi thành danh sách vật liệu
      final materials = <Map<String, dynamic>>[];

      for (int i = 1; i < rowsAsListOfValues.length; i++) {
        final row = rowsAsListOfValues[i];
        if (row.length >= 4) {
          materials.add({
            'name': row[0].toString(),
            'type': _getMaterialTypeFromName(row[1].toString()),
            'unit': row[2].toString(),
            'pricePerUnit': double.tryParse(row[3].toString()) ?? 0.0,
          });
        }
      }

      return materials;
    } catch (e) {
      print('Lỗi khi nhập CSV: $e');
      return null;
    }
  }

  /// Lấy tên loại vật liệu
  String _getMaterialTypeName(model.MaterialType type) {
    switch (type) {
      case model.MaterialType.brick:
        return 'Gạch';
      case model.MaterialType.sand:
        return 'Cát';
      case model.MaterialType.cement:
        return 'Xi măng';
      case model.MaterialType.steel:
        return 'Thép';
      case model.MaterialType.stone:
        return 'Đá';
      case model.MaterialType.tile:
        return 'Gạch ốp lát';
      case model.MaterialType.roofTile:
        return 'Ngói';
      case model.MaterialType.metalSheet:
        return 'Tôn';
      case model.MaterialType.insulatedMetal:
        return 'Tôn xốp';
      case model.MaterialType.gypsum:
        return 'Thạch cao';
      case model.MaterialType.exteriorPaint:
        return 'Sơn ngoại thất';
      case model.MaterialType.interiorPaint:
        return 'Sơn nội thất';
      case model.MaterialType.aluminumDoor:
        return 'Cửa nhôm';
      case model.MaterialType.compositeDoor:
        return 'Cửa composite';
      case model.MaterialType.labor:
        return 'Nhân công';
      case model.MaterialType.plumbingLabor:
        return 'Nhân công điện nước';
      case model.MaterialType.plumbingMaterial:
        return 'Vật tư điện nước';
      case model.MaterialType.concreteSand:
        return 'Cát bê tông';
      case model.MaterialType.custom:
        return 'Tùy chỉnh';
    }
  }

  /// Lấy loại vật liệu từ tên
  model.MaterialType _getMaterialTypeFromName(String name) {
    switch (name.toLowerCase()) {
      case 'gạch':
        return model.MaterialType.brick;
      case 'cát':
        return model.MaterialType.sand;
      case 'xi măng':
        return model.MaterialType.cement;
      case 'thép':
        return model.MaterialType.steel;
      case 'đá':
        return model.MaterialType.stone;
      case 'gạch ốp lát':
        return model.MaterialType.tile;
      case 'ngói':
        return model.MaterialType.roofTile;
      case 'tôn':
        return model.MaterialType.metalSheet;
      case 'tôn xốp':
        return model.MaterialType.insulatedMetal;
      case 'thạch cao':
        return model.MaterialType.gypsum;
      case 'sơn ngoại thất':
        return model.MaterialType.exteriorPaint;
      case 'sơn nội thất':
        return model.MaterialType.interiorPaint;
      case 'cửa nhôm':
        return model.MaterialType.aluminumDoor;
      case 'cửa composite':
        return model.MaterialType.compositeDoor;
      case 'nhân công':
        return model.MaterialType.labor;
      case 'nhân công điện nước':
        return model.MaterialType.plumbingLabor;
      case 'vật tư điện nước':
        return model.MaterialType.plumbingMaterial;
      case 'cát bê tông':
        return model.MaterialType.concreteSand;
      default:
        return model.MaterialType.custom;
    }
  }
}
