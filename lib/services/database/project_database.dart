import 'dart:convert';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../../models/project/project_model.dart';
import '../../models/project/building_model.dart';

/// Service quản lý database cho dự án
class ProjectDatabase {
  static final ProjectDatabase _instance = ProjectDatabase._internal();
  
  /// Singleton pattern
  factory ProjectDatabase() => _instance;
  
  ProjectDatabase._internal();
  
  /// Database instance
  Database? _database;
  
  /// Tên bảng dự án
  static const String tableProjects = 'projects';
  
  /// Lấy database instance
  Future<Database> get database async {
    if (_database != null) return _database!;
    
    _database = await _initDatabase();
    return _database!;
  }
  
  /// Khởi tạo database
  Future<Database> _initDatabase() async {
    final databasesPath = await getDatabasesPath();
    final path = join(databasesPath, 'nha_save.db');
    
    return await openDatabase(
      path,
      version: 2,
      onCreate: _createDatabase,
      onUpgrade: _upgradeDatabase,
    );
  }
  
  /// Tạo cấu trúc database
  Future<void> _createDatabase(Database db, int version) async {
    await db.execute('''
      CREATE TABLE $tableProjects (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        location TEXT NOT NULL,
        imagePath TEXT,
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL,
        floors TEXT,
        roof TEXT,
        foundationType INTEGER,
        structureType INTEGER,
        selectedMaterialIds TEXT,
        detailedParameters TEXT,
        results TEXT
      )
    ''');
  }
  
  /// Nâng cấp database
  Future<void> _upgradeDatabase(Database db, int oldVersion, int newVersion) async {
    if (oldVersion < 2) {
      // Thêm các cột mới
      await db.execute('ALTER TABLE $tableProjects ADD COLUMN floors TEXT');
      await db.execute('ALTER TABLE $tableProjects ADD COLUMN roof TEXT');
      await db.execute('ALTER TABLE $tableProjects ADD COLUMN detailedParameters TEXT');
      
      // Đổi tên cột parameters thành detailedParameters nếu cần
      final List<Map<String, dynamic>> columns = await db.rawQuery('PRAGMA table_info($tableProjects)');
      final bool hasParametersColumn = columns.any((column) => column['name'] == 'parameters');
      
      if (hasParametersColumn) {
        // Tạo bảng tạm thời
        await db.execute('''
          CREATE TABLE ${tableProjects}_temp (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            location TEXT NOT NULL,
            imagePath TEXT,
            createdAt TEXT NOT NULL,
            updatedAt TEXT NOT NULL,
            floors TEXT,
            roof TEXT,
            foundationType INTEGER,
            structureType INTEGER,
            selectedMaterialIds TEXT,
            detailedParameters TEXT,
            results TEXT
          )
        ''');
        
        // Sao chép dữ liệu, chuyển đổi parameters thành detailedParameters
        await db.execute('''
          INSERT INTO ${tableProjects}_temp
          SELECT id, name, location, imagePath, createdAt, updatedAt, 
                 NULL as floors, NULL as roof, foundationType, structureType, 
                 selectedMaterialIds, parameters as detailedParameters, results
          FROM $tableProjects
        ''');
        
        // Xóa bảng cũ
        await db.execute('DROP TABLE $tableProjects');
        
        // Đổi tên bảng tạm thời
        await db.execute('ALTER TABLE ${tableProjects}_temp RENAME TO $tableProjects');
      }
    }
  }
  
  /// Lấy tất cả dự án
  Future<List<Project>> getAllProjects() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(tableProjects);
    
    return List.generate(maps.length, (i) {
      final map = maps[i];
      
      // Chuyển đổi các trường JSON
      final selectedMaterialIds = jsonDecode(map['selectedMaterialIds'] ?? '[]') as List;
      final floors = map['floors'] != null ? jsonDecode(map['floors']) : null;
      final roof = map['roof'] != null ? jsonDecode(map['roof']) : null;
      final detailedParameters = map['detailedParameters'] != null 
          ? jsonDecode(map['detailedParameters']) 
          : {};
      final results = jsonDecode(map['results'] ?? '{}');
      
      return Project(
        id: map['id'],
        name: map['name'],
        location: map['location'],
        imagePath: map['imagePath'],
        createdAt: DateTime.parse(map['createdAt']),
        updatedAt: DateTime.parse(map['updatedAt']),
        floors: floors != null 
            ? (floors as List).map((f) => Floor.fromMap(Map<String, dynamic>.from(f))).toList() 
            : [Floor(number: 1, area: 0, height: 0)],
        roof: roof != null 
            ? Roof.fromMap(Map<String, dynamic>.from(roof)) 
            : null,
        foundationType: map['foundationType'] != null 
            ? FoundationType.values[map['foundationType']] 
            : null,
        structureType: map['structureType'] != null 
            ? StructureType.values[map['structureType']] 
            : null,
        selectedMaterialIds: selectedMaterialIds.cast<String>(),
        detailedParameters: DetailedParameters.fromMap(Map<String, dynamic>.from(detailedParameters)),
        results: Map<String, dynamic>.from(results),
      );
    });
  }
  
  /// Lấy dự án theo ID
  Future<Project?> getProject(String id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      tableProjects,
      where: 'id = ?',
      whereArgs: [id],
    );
    
    if (maps.isEmpty) return null;
    
    final map = maps.first;
    
    // Chuyển đổi các trường JSON
    final selectedMaterialIds = jsonDecode(map['selectedMaterialIds'] ?? '[]') as List;
    final floors = map['floors'] != null ? jsonDecode(map['floors']) : null;
    final roof = map['roof'] != null ? jsonDecode(map['roof']) : null;
    final detailedParameters = map['detailedParameters'] != null 
        ? jsonDecode(map['detailedParameters']) 
        : {};
    final results = jsonDecode(map['results'] ?? '{}');
    
    return Project(
      id: map['id'],
      name: map['name'],
      location: map['location'],
      imagePath: map['imagePath'],
      createdAt: DateTime.parse(map['createdAt']),
      updatedAt: DateTime.parse(map['updatedAt']),
      floors: floors != null 
          ? (floors as List).map((f) => Floor.fromMap(Map<String, dynamic>.from(f))).toList() 
          : [Floor(number: 1, area: 0, height: 0)],
      roof: roof != null 
          ? Roof.fromMap(Map<String, dynamic>.from(roof)) 
          : null,
      foundationType: map['foundationType'] != null 
          ? FoundationType.values[map['foundationType']] 
          : null,
      structureType: map['structureType'] != null 
          ? StructureType.values[map['structureType']] 
          : null,
      selectedMaterialIds: selectedMaterialIds.cast<String>(),
      detailedParameters: DetailedParameters.fromMap(Map<String, dynamic>.from(detailedParameters)),
      results: Map<String, dynamic>.from(results),
    );
  }
  
  /// Thêm dự án mới
  Future<String> insertProject(Project project) async {
    final db = await database;
    
    // Chuyển đổi các trường phức tạp thành JSON
    final projectMap = {
      'id': project.id,
      'name': project.name,
      'location': project.location,
      'imagePath': project.imagePath,
      'createdAt': project.createdAt.toIso8601String(),
      'updatedAt': project.updatedAt.toIso8601String(),
      'floors': jsonEncode(project.floors.map((f) => f.toMap()).toList()),
      'roof': project.roof != null ? jsonEncode(project.roof!.toMap()) : null,
      'foundationType': project.foundationType?.index,
      'structureType': project.structureType?.index,
      'selectedMaterialIds': jsonEncode(project.selectedMaterialIds),
      'detailedParameters': jsonEncode(project.detailedParameters.toMap()),
      'results': jsonEncode(project.results),
    };
    
    await db.insert(tableProjects, projectMap);
    return project.id;
  }
  
  /// Cập nhật dự án
  Future<void> updateProject(Project project) async {
    final db = await database;
    
    // Cập nhật thời gian
    project.updatedAt = DateTime.now();
    
    // Chuyển đổi các trường phức tạp thành JSON
    final projectMap = {
      'name': project.name,
      'location': project.location,
      'imagePath': project.imagePath,
      'updatedAt': project.updatedAt.toIso8601String(),
      'floors': jsonEncode(project.floors.map((f) => f.toMap()).toList()),
      'roof': project.roof != null ? jsonEncode(project.roof!.toMap()) : null,
      'foundationType': project.foundationType?.index,
      'structureType': project.structureType?.index,
      'selectedMaterialIds': jsonEncode(project.selectedMaterialIds),
      'detailedParameters': jsonEncode(project.detailedParameters.toMap()),
      'results': jsonEncode(project.results),
    };
    
    await db.update(
      tableProjects,
      projectMap,
      where: 'id = ?',
      whereArgs: [project.id],
    );
  }
  
  /// Xóa dự án
  Future<void> deleteProject(String id) async {
    final db = await database;
    
    await db.delete(
      tableProjects,
      where: 'id = ?',
      whereArgs: [id],
    );
  }
}
