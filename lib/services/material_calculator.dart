/// <PERSON><PERSON>c hằng số kỹ thuật tách riêng để dễ chỉnh sửa
class _Constants {
  // Hằng số cho vữa
  static const double waterPerCement = 0.4; // m³ nước / bao xi măng

  // Hằng số cho tường
  static const double mortarRatioInWall = 0.3; // 30% thể tích tường là vữa
  static const double cementRatioInMortar = 0.25; // 25% thể tích vữa là xi măng
  static const double sandRatioInMortar = 0.75; // 75% thể tích vữa là cát
  static const double plasterThickness = 0.015; // Độ dày lớp trát 1.5cm
}

/// Lớp tính toán vật liệu xây dựng
class MaterialCalculator {
  /// Chuyển đổi giá trị sang kiểu double
  static double _toDouble(dynamic value) {
    if (value is int) return value.toDouble();
    if (value is double) return value;
    if (value is String) return double.tryParse(value) ?? 0.0;
    return 0.0;
  }

  /// Tính số viên gạch trên mỗi m²
  ///
  /// [brickLength]: Chiều dài viên gạch (m)
  /// [brickWidth]: Chiều rộng viên gạch (m)
  /// [brickHeight]: Chiều cao viên gạch (m)
  /// [wallType]: Loại tường ("10" hoặc "20")
  static double calculateBricksPerSquareMeter(
    double brickLength,
    double brickWidth,
    double brickHeight,
    String wallType,
  ) {
    // Tính diện tích bề mặt viên gạch (m²)
    double brickSurfaceArea = brickLength * brickHeight;

    // Tính số viên gạch trên mỗi m²
    double bricksPerM2 = 1 / brickSurfaceArea;

    // Điều chỉnh theo loại tường
    if (wallType == "10") {
      // Tường 10cm sử dụng 1 lớp gạch
      return bricksPerM2;
    } else if (wallType == "20") {
      // Tường 20cm sử dụng 2 lớp gạch
      return 2 * bricksPerM2;
    } else {
      throw Exception("Loại tường không hợp lệ. Chọn '10' hoặc '20'.");
    }
  }

  /// Tính số lượng gạch
  ///
  /// [area]: Diện tích tường (m²)
  /// [bricksPerM2]: Số viên gạch trên mỗi m²
  static double calculateBrickQuantity(double area, double bricksPerM2) {
    return area * bricksPerM2;
  }

  /// Tính thể tích vữa xây
  ///
  /// [area]: Diện tích tường (m²)
  /// [wallType]: Loại tường ("10" hoặc "20")
  static double calculateMortarVolumeForBuilding(double area, String wallType) {
    double thickness = wallType == "10" ? 0.1 : 0.2;
    return area * thickness * _Constants.mortarRatioInWall;
  }

  /// Tính thể tích vữa trát
  ///
  /// [area]: Diện tích tường (m²)
  /// [plasteringSides]: Số mặt trát (1 hoặc 2)
  static double calculateMortarVolumeForPlastering(
    double area,
    int plasteringSides,
  ) {
    return area * plasteringSides * _Constants.plasterThickness;
  }

  /// Tính khối lượng xi măng và cát
  ///
  /// [buildingMortar]: Thể tích vữa xây (m³)
  /// [plasteringMortar]: Thể tích vữa trát (m³)
  static Map<String, double> calculateCementAndSand(
    double buildingMortar,
    double plasteringMortar,
  ) {
    double totalMortar = buildingMortar + plasteringMortar;
    return {
      "cement": totalMortar * _Constants.cementRatioInMortar, // Xi măng (m³)
      "sand": totalMortar * _Constants.sandRatioInMortar, // Cát (m³)
    };
  }

  /// Tính lượng nước
  ///
  /// [buildingMortar]: Thể tích vữa xây (m³)
  /// [plasteringMortar]: Thể tích vữa trát (m³)
  static double calculateWater(double buildingMortar, double plasteringMortar) {
    double totalCement =
        (buildingMortar + plasteringMortar) * _Constants.cementRatioInMortar;
    return totalCement * _Constants.waterPerCement;
  }

  /// Hàm tổng hợp tính toán toàn bộ vật liệu
  ///
  /// [wallArea]: Diện tích tường (m²)
  /// [brickLength]: Chiều dài viên gạch (m)
  /// [brickWidth]: Chiều rộng viên gạch (m)
  /// [brickHeight]: Chiều cao viên gạch (m)
  /// [wallType]: Loại tường ("10" hoặc "20")
  /// [plasterSides]: Số mặt trát (1 hoặc 2)
  static Map<String, double> calculateMaterials({
    required double wallArea,
    required double brickLength,
    required double brickWidth,
    required double brickHeight,
    required String wallType,
    required int plasterSides,
  }) {
    // Tính số viên gạch trên mỗi m²
    double bricksPerM2 = calculateBricksPerSquareMeter(
      brickLength,
      brickWidth,
      brickHeight,
      wallType,
    );

    // Tính số lượng gạch
    double brickQuantity = calculateBrickQuantity(wallArea, bricksPerM2);

    // Tính thể tích vữa xây
    double buildingMortar = calculateMortarVolumeForBuilding(
      wallArea,
      wallType,
    );

    // Tính thể tích vữa trát
    double plasteringMortar = calculateMortarVolumeForPlastering(
      wallArea,
      plasterSides,
    );

    // Tính xi măng và cát
    Map<String, double> cementAndSand = calculateCementAndSand(
      buildingMortar,
      plasteringMortar,
    );

    // Tính nước
    double water = calculateWater(buildingMortar, plasteringMortar);

    // Trả về kết quả
    return {
      "bricks": brickQuantity,
      "cement": cementAndSand["cement"]!,
      "sand": cementAndSand["sand"]!,
      "water": water,
    };
  }

  /// Tính tổng diện tích các tầng từ danh sách tầng
  ///
  /// [floors]: Danh sách các tầng
  static double calculateTotalFloorArea(List<dynamic> floors) {
    double totalArea = 0.0;
    for (final floor in floors) {
      totalArea += _toDouble(floor['area']);
    }
    return totalArea;
  }

  /// Tính toán vật liệu dựa trên thông số chi tiết
  ///
  /// [detailedParams]: Thông số chi tiết từ người dùng
  /// [selectedMaterialIds]: Danh sách ID vật liệu được chọn
  /// [brickDimensions]: Kích thước viên gạch (chiều dài, chiều rộng, chiều cao)
  /// [floors]: Danh sách các tầng từ dự án (để tính diện tích cho nhân công)
  static Map<String, dynamic> calculateMaterialsFromDetailedParams(
    Map<String, dynamic> detailedParams, {
    List<String>? selectedMaterialIds,
    Map<String, double>? brickDimensions,
    List<dynamic>? floors,
  }) {
    // Kết quả tính toán
    Map<String, Map<String, double>> intermediateResults = {};
    Map<String, double> quantities = {};
    Map<String, double> costs = {};

    // Thông số gạch mặc định (m) - có thể được ghi đè bởi thông số từ MaterialProvider
    double brickLength = brickDimensions?['length'] ?? 0.22;
    double brickWidth = brickDimensions?['width'] ?? 0.10;
    double brickHeight = brickDimensions?['height'] ?? 0.05;

    // Tính toán vật liệu cho tường
    if (detailedParams.containsKey('walls')) {
      Map<String, dynamic> wallsData = detailedParams['walls'];

      // Tổng vật liệu cho tường
      Map<String, double> totalWallMaterials = {
        'bricks': 0.0,
        'cement': 0.0,
        'sand': 0.0,
        'plasterSand': 0.0,
        'water': 0.0,
      };

      // Xử lý danh sách tường
      if (wallsData.containsKey('walls')) {
        final List<dynamic> walls = wallsData['walls'] as List<dynamic>;

        for (final wall in walls) {
          final String wallType = wall['type'] as String;
          final int plasterSides = wall['plasterSides'] as int;
          final double length = _toDouble(wall['length']);
          final double height = _toDouble(wall['height']);
          final double area = _toDouble(wall['area']);

          if (area > 0) {
            // Tính vật liệu cho từng tường
            Map<String, double> wallMaterials = calculateMaterials(
              wallArea: area,
              brickLength: brickLength,
              brickWidth: brickWidth,
              brickHeight: brickHeight,
              wallType: wallType,
              plasterSides: plasterSides,
            );

            // Cộng dồn vào tổng vật liệu
            for (String material in wallMaterials.keys) {
              totalWallMaterials[material] =
                  (totalWallMaterials[material] ?? 0) +
                  (wallMaterials[material] ?? 0);
            }

            // Lưu kết quả trung gian cho từng tường
            intermediateResults['wall_${wallType}_${length}x$height'] =
                wallMaterials;
          }
        }
      }
      // Tương thích với dữ liệu cũ
      else if (wallsData.containsKey('area')) {
        double wallArea = _toDouble(wallsData['area']);

        if (wallArea > 0) {
          // Tính cho tường 10 trát 2 mặt
          Map<String, double> wall10Materials = calculateMaterials(
            wallArea: wallArea,
            brickLength: brickLength,
            brickWidth: brickWidth,
            brickHeight: brickHeight,
            wallType: "10",
            plasterSides: 2,
          );

          intermediateResults['wall10'] = wall10Materials;

          // Tính cho tường 20 trát 2 mặt
          Map<String, double> wall20Materials = calculateMaterials(
            wallArea: wallArea,
            brickLength: brickLength,
            brickWidth: brickWidth,
            brickHeight: brickHeight,
            wallType: "20",
            plasterSides: 2,
          );

          intermediateResults['wall20'] = wall20Materials;

          // Giả định 70% tường là tường 10, 30% là tường 20
          for (String material in wall10Materials.keys) {
            totalWallMaterials[material] =
                (wall10Materials[material] ?? 0) * 0.7 +
                (wall20Materials[material] ?? 0) * 0.3;
          }
        }
      }

      // Lưu tổng vật liệu cho tường
      intermediateResults['walls'] = totalWallMaterials;

      // Thêm vào kết quả cuối cùng
      if (selectedMaterialIds?.contains('Gạch xây') ?? false) {
        quantities['Gạch xây'] = double.parse(
          (totalWallMaterials['bricks'] ?? 0).toStringAsFixed(2),
        );
      }

      if (selectedMaterialIds?.contains('Xi măng') ?? false) {
        quantities['Xi măng'] = double.parse(
          ((totalWallMaterials['cement'] ?? 0) + (quantities['Xi măng'] ?? 0))
              .toStringAsFixed(2),
        );
      }

      if (selectedMaterialIds?.contains('Cát xây') ?? false) {
        quantities['Cát xây'] = double.parse(
          ((totalWallMaterials['sand'] ?? 0) + (quantities['Cát xây'] ?? 0))
              .toStringAsFixed(2),
        );
      }

      if (selectedMaterialIds?.contains('Cát trát') ?? false) {
        quantities['Cát trát'] = double.parse(
          (totalWallMaterials['plasterSand'] ?? 0).toStringAsFixed(2),
        );
      }

      if (selectedMaterialIds?.contains('Đá') ?? false) {
        // Giả định đá là 2 lần lượng xi măng
        quantities['Đá'] = double.parse(
          ((totalWallMaterials['cement'] ?? 0) * 2 + (quantities['Đá'] ?? 0))
              .toStringAsFixed(2),
        );
      }

      if (selectedMaterialIds?.contains('Nước') ?? false) {
        quantities['Nước'] = double.parse(
          (totalWallMaterials['water'] ?? 0).toStringAsFixed(2),
        );
      }
    }

    // Tính toán vật liệu cho móng
    if (detailedParams.containsKey('foundation')) {
      Map<String, dynamic> foundation = detailedParams['foundation'];
      double totalFoundationLength = 0.0;

      // Xử lý danh sách chiều dài móng
      if (foundation.containsKey('lengths')) {
        final List<dynamic> foundationLengths =
            foundation['lengths'] as List<dynamic>;
        for (final length in foundationLengths) {
          totalFoundationLength += _toDouble(length);
        }
      }
      // Tương thích với dữ liệu cũ
      else if (foundation.containsKey('length')) {
        totalFoundationLength = _toDouble(foundation['length']);
      }

      if (totalFoundationLength > 0) {
        // Giả định chiều rộng móng là 0.3m và chiều cao là 0.5m
        double foundationArea = totalFoundationLength * 0.3;

        Map<String, double> foundationMaterials = {
          'concrete': foundationArea * 0.5, // Thể tích bê tông (m³)
          'steel':
              foundationArea *
              0.5 *
              80, // Khối lượng thép (kg), giả định 80kg/m³
        };

        intermediateResults['foundation'] = foundationMaterials;

        if (selectedMaterialIds?.contains('Bê tông') ?? false) {
          quantities['Bê tông'] = double.parse(
            (foundationMaterials['concrete'] ?? 0).toStringAsFixed(2),
          );
        }

        if (selectedMaterialIds?.contains('Thép') ?? false) {
          quantities['Thép'] = double.parse(
            (foundationMaterials['steel'] ?? 0).toStringAsFixed(2),
          );
        }

        if (selectedMaterialIds?.contains('Đá') ?? false) {
          // Giả định đá chiếm 70% thể tích bê tông
          quantities['Đá'] = double.parse(
            ((foundationMaterials['concrete'] ?? 0) * 0.7).toStringAsFixed(2),
          );
        }

        if (selectedMaterialIds?.contains('Xi măng') ?? false) {
          // Giả định xi măng chiếm 15% thể tích bê tông
          quantities['Xi măng'] = double.parse(
            ((foundationMaterials['concrete'] ?? 0) * 0.15).toStringAsFixed(2),
          );
        }

        if (selectedMaterialIds?.contains('Cát xây') ?? false) {
          // Giả định cát xây chiếm 15% thể tích bê tông
          quantities['Cát xây'] = double.parse(
            ((foundationMaterials['concrete'] ?? 0) * 0.15).toStringAsFixed(2),
          );
        }
      }
    }

    // Tính toán vật liệu cho cửa
    if (detailedParams.containsKey('doors')) {
      Map<String, dynamic> doors = detailedParams['doors'];
      double totalWindowArea = 0.0;
      double totalDoorArea = 0.0;
      double totalRollingDoorArea = 0.0;
      double totalDoorAreaAll = 0.0;

      // Xử lý cửa sổ
      if (doors.containsKey('windows')) {
        final List<dynamic> windows = doors['windows'] as List<dynamic>;
        for (final window in windows) {
          final double width = _toDouble(window['width']);
          final double height = _toDouble(window['height']);
          final int quantity = window['quantity'] as int;
          final double area = width * height * quantity;
          totalWindowArea += area;
        }
      } else if (doors.containsKey('windowArea')) {
        totalWindowArea = _toDouble(doors['windowArea']);
      }

      // Xử lý cửa đi
      if (doors.containsKey('doors')) {
        final List<dynamic> doorsList = doors['doors'] as List<dynamic>;
        for (final door in doorsList) {
          final double width = _toDouble(door['width']);
          final double height = _toDouble(door['height']);
          final int quantity = door['quantity'] as int;
          final double area = width * height * quantity;
          totalDoorArea += area;
        }
      } else if (doors.containsKey('doorArea')) {
        totalDoorArea = _toDouble(doors['doorArea']);
      }

      // Xử lý cửa cuốn
      if (doors.containsKey('rollingDoors')) {
        final List<dynamic> rollingDoors =
            doors['rollingDoors'] as List<dynamic>;
        for (final rollingDoor in rollingDoors) {
          final double width = _toDouble(rollingDoor['width']);
          final double height = _toDouble(rollingDoor['height']);
          final int quantity = rollingDoor['quantity'] as int;
          final double area = width * height * quantity;
          totalRollingDoorArea += area;
        }
      } else if (doors.containsKey('rollingDoorArea')) {
        totalRollingDoorArea = _toDouble(doors['rollingDoorArea']);
      }

      // Tính tổng diện tích cửa
      totalDoorAreaAll = totalWindowArea + totalDoorArea + totalRollingDoorArea;

      if (totalDoorAreaAll > 0) {
        // Tính vật liệu cho từng loại cửa
        Map<String, double> windowMaterials = {
          'aluminum':
              totalWindowArea * 3, // Khối lượng nhôm (kg), giả định 3kg/m²
          'glass':
              totalWindowArea *
              0.8, // Diện tích kính (m²), giả định 80% diện tích
        };

        Map<String, double> doorMaterials = {
          'aluminum':
              totalDoorArea * 5, // Khối lượng nhôm (kg), giả định 5kg/m²
          'glass':
              totalDoorArea *
              0.5, // Diện tích kính (m²), giả định 50% diện tích
          'wood': totalDoorArea * 0.3, // Khối lượng gỗ (m³), giả định 0.3m³/m²
        };

        Map<String, double> rollingDoorMaterials = {
          'steel':
              totalRollingDoorArea *
              10, // Khối lượng thép (kg), giả định 10kg/m²
        };

        // Tổng hợp vật liệu cho tất cả các loại cửa
        Map<String, double> allDoorMaterials = {
          'aluminum':
              (windowMaterials['aluminum'] ?? 0) +
              (doorMaterials['aluminum'] ?? 0),
          'glass':
              (windowMaterials['glass'] ?? 0) + (doorMaterials['glass'] ?? 0),
          'wood': doorMaterials['wood'] ?? 0,
          'steel': rollingDoorMaterials['steel'] ?? 0,
        };

        // Lưu kết quả trung gian
        intermediateResults['windows'] = windowMaterials;
        intermediateResults['doors'] = doorMaterials;
        intermediateResults['rollingDoors'] = rollingDoorMaterials;
        intermediateResults['allDoors'] = allDoorMaterials;

        // Thêm vào kết quả cuối cùng
        if (selectedMaterialIds?.contains('Nhôm') ?? false) {
          quantities['Nhôm'] = double.parse(
            (allDoorMaterials['aluminum'] ?? 0).toStringAsFixed(2),
          );
        }

        if (selectedMaterialIds?.contains('Kính') ?? false) {
          quantities['Kính'] = double.parse(
            (allDoorMaterials['glass'] ?? 0).toStringAsFixed(2),
          );
        }

        if (selectedMaterialIds?.contains('Gỗ') ?? false) {
          quantities['Gỗ'] = double.parse(
            (allDoorMaterials['wood'] ?? 0).toStringAsFixed(2),
          );
        }

        if (selectedMaterialIds?.contains('Thép') ?? false) {
          quantities['Thép'] = double.parse(
            ((allDoorMaterials['steel'] ?? 0) + (quantities['Thép'] ?? 0))
                .toStringAsFixed(2),
          );
        }
      }
    }

    // Tính toán vật liệu cho nhà vệ sinh
    if (detailedParams.containsKey('others')) {
      Map<String, dynamic> others = detailedParams['others'];

      // Xử lý nhà vệ sinh
      double totalBathroomArea = 0.0;

      if (others.containsKey('bathrooms')) {
        final List<dynamic> bathrooms = others['bathrooms'] as List<dynamic>;
        for (final bathroom in bathrooms) {
          final double area = _toDouble(bathroom['area']);
          totalBathroomArea += area;
        }
      } else if (others.containsKey('bathroomCount')) {
        // Tương thích với dữ liệu cũ
        final int bathroomCount = others['bathroomCount'] as int;
        // Giả định mỗi phòng vệ sinh có diện tích 4m²
        totalBathroomArea = bathroomCount * 4.0;
      }

      if (totalBathroomArea > 0) {
        // Giả định nhà vệ sinh sử dụng tường 10cm, chát 2 mặt
        // Tính toán vật liệu tường cho nhà vệ sinh
        double wallHeight = 2.5; // Giả định chiều cao tường 2.5m
        double wallLength =
            totalBathroomArea / wallHeight; // Ước tính chiều dài tường

        // Tính diện tích tường (không bao gồm cửa)
        double wallArea = wallLength * wallHeight;

        // Tính thể tích tường
        double wallThickness = 0.1; // Tường 10cm
        double wallVolume = wallArea * wallThickness;

        // Tính số lượng gạch
        double brickVolume = 0.2 * 0.1 * 0.065; // Thể tích một viên gạch (m³)
        int brickCount =
            (wallVolume / brickVolume * 1.05).round(); // Thêm 5% dự phòng

        // Tính lượng vữa xây
        double mortarVolume = wallVolume * 0.3; // 30% thể tích tường là vữa

        // Tính lượng xi măng và cát cho vữa xây
        double cementForMortar =
            mortarVolume * 0.25; // 25% thể tích vữa là xi măng
        double sandForMortar = mortarVolume * 0.75; // 75% thể tích vữa là cát

        // Tính lượng vữa trát
        double plasterArea = wallArea * 2; // Trát 2 mặt
        double plasterThickness = 0.015; // Độ dày lớp trát 1.5cm
        double plasterVolume = plasterArea * plasterThickness;

        // Tính lượng xi măng và cát cho vữa trát
        double cementForPlaster =
            plasterVolume * 0.25; // 25% thể tích vữa là xi măng
        double sandForPlaster = plasterVolume * 0.75; // 75% thể tích vữa là cát

        // Tổng hợp vật liệu
        Map<String, double> bathroomWallMaterials = {
          'brick': brickCount.toDouble(),
          'cement': cementForMortar + cementForPlaster,
          'sand': sandForMortar + sandForPlaster,
        };

        // Giả định nhà vệ sinh sử dụng gạch men
        Map<String, double> bathroomTileMaterials = {
          'tile':
              totalBathroomArea *
              1.1, // Diện tích gạch men (m²), thêm 10% dự phòng
          'tileAdhesive':
              totalBathroomArea * 0.005, // Khối lượng keo dán gạch (m³)
        };

        // Giả định nhà vệ sinh có thiết bị vệ sinh
        // Tính số lượng thiết bị vệ sinh dựa trên diện tích
        int bathroomCount =
            (totalBathroomArea / 4.0).ceil(); // Giả định mỗi phòng vệ sinh 4m²

        Map<String, double> bathroomFixtureMaterials = {
          'toilet': bathroomCount.toDouble(), // Số lượng bồn cầu
          'sink': bathroomCount.toDouble(), // Số lượng bồn rửa
          'shower': bathroomCount.toDouble(), // Số lượng vòi sen
        };

        // Lưu kết quả trung gian
        intermediateResults['bathroomWall'] = bathroomWallMaterials;
        intermediateResults['bathroomTile'] = bathroomTileMaterials;
        intermediateResults['bathroomFixture'] = bathroomFixtureMaterials;

        // Cộng dồn vật liệu tường
        if (intermediateResults.containsKey('wall')) {
          Map<String, double> wallMaterials =
              intermediateResults['wall'] as Map<String, double>;
          for (final entry in bathroomWallMaterials.entries) {
            wallMaterials[entry.key] =
                (wallMaterials[entry.key] ?? 0) + entry.value;
          }
        } else {
          intermediateResults['wall'] = bathroomWallMaterials;
        }

        // Thêm vào kết quả cuối cùng
        if (selectedMaterialIds?.contains('Gạch men') ?? false) {
          quantities['Gạch men'] = double.parse(
            (bathroomTileMaterials['tile'] ?? 0).toStringAsFixed(2),
          );
        }

        if (selectedMaterialIds?.contains('Keo dán gạch') ?? false) {
          quantities['Keo dán gạch'] = double.parse(
            (bathroomTileMaterials['tileAdhesive'] ?? 0).toStringAsFixed(2),
          );
        }
      }

      // Xử lý thạch cao
      double gypsumCeilingArea = 0.0;

      if (others.containsKey('gypsumCeilingArea')) {
        gypsumCeilingArea = _toDouble(others['gypsumCeilingArea']);
      }

      if (gypsumCeilingArea > 0) {
        Map<String, double> gypsumMaterials = {
          'gypsumBoard':
              gypsumCeilingArea *
              1.1, // Diện tích tấm thạch cao (m²), thêm 10% dự phòng
          'steelFrame':
              gypsumCeilingArea *
              2.5, // Khối lượng khung thép (kg), giả định 2.5kg/m²
          'gypsumCeilingArea':
              gypsumCeilingArea, // Thêm diện tích thạch cao từ Step5
        };

        // Lưu kết quả trung gian
        intermediateResults['gypsumCeiling'] = gypsumMaterials;
        intermediateResults['gypsum'] = {
          'gypsumCeilingArea': gypsumCeilingArea,
        };

        // Thêm vào kết quả cuối cùng
        if (selectedMaterialIds?.contains('Tấm thạch cao') ?? false) {
          quantities['Tấm thạch cao'] = double.parse(
            (gypsumMaterials['gypsumBoard'] ?? 0).toStringAsFixed(2),
          );
        }

        if (selectedMaterialIds?.contains('Thạch cao') ?? false) {
          quantities['Thạch cao'] = double.parse(
            gypsumCeilingArea.toStringAsFixed(2),
          );
        }

        if (selectedMaterialIds?.contains('Thép') ?? false) {
          quantities['Thép'] = double.parse(
            ((gypsumMaterials['steelFrame'] ?? 0) + (quantities['Thép'] ?? 0))
                .toStringAsFixed(2),
          );
        }
      }

      // Xử lý cầu thang
      int totalStairsSteps = 0;

      if (others.containsKey('stairs')) {
        final List<dynamic> stairs = others['stairs'] as List<dynamic>;
        for (final stair in stairs) {
          final int steps = stair['steps'] as int;
          totalStairsSteps += steps;
        }
      } else if (others.containsKey('stairsSteps')) {
        // Tương thích với dữ liệu cũ
        totalStairsSteps = others['stairsSteps'] as int;
      }

      if (totalStairsSteps > 0) {
        // Giả định mỗi bậc cầu thang cần 0.05m³ bê tông và 5kg thép
        Map<String, double> stairsMaterials = {
          'concrete': totalStairsSteps * 0.05, // Thể tích bê tông (m³)
          'steel': totalStairsSteps * 5.0, // Khối lượng thép (kg)
        };

        // Lưu kết quả trung gian
        intermediateResults['stairs'] = stairsMaterials;

        // Thêm vào kết quả cuối cùng
        if (selectedMaterialIds?.contains('Bê tông') ?? false) {
          quantities['Bê tông'] = double.parse(
            ((stairsMaterials['concrete'] ?? 0) + (quantities['Bê tông'] ?? 0))
                .toStringAsFixed(2),
          );
        }

        if (selectedMaterialIds?.contains('Thép') ?? false) {
          quantities['Thép'] = double.parse(
            ((stairsMaterials['steel'] ?? 0) + (quantities['Thép'] ?? 0))
                .toStringAsFixed(2),
          );
        }
      }
    }

    // Tính toán nhân công dựa trên tổng diện tích các tầng
    double totalFloorArea = 0.0;
    if (floors != null && floors.isNotEmpty) {
      totalFloorArea = calculateTotalFloorArea(floors);

      // Lưu kết quả trung gian cho nhân công và vật tư điện nước
      Map<String, double> laborMaterials = {'area': totalFloorArea};
      Map<String, double> plumbingLaborMaterials = {'area': totalFloorArea};
      Map<String, double> plumbingMaterials = {'area': totalFloorArea};

      intermediateResults['labor'] = laborMaterials;
      intermediateResults['plumbingLabor'] = plumbingLaborMaterials;
      intermediateResults['plumbingMaterial'] = plumbingMaterials;

      // Thêm vào kết quả cuối cùng
      if (selectedMaterialIds?.contains('Nhân công xây dựng') ?? false) {
        quantities['Nhân công xây dựng'] = double.parse(
          totalFloorArea.toStringAsFixed(2),
        );
      }

      if (selectedMaterialIds?.contains('Nhân công điện nước') ?? false) {
        quantities['Nhân công điện nước'] = double.parse(
          totalFloorArea.toStringAsFixed(2),
        );
      }

      if (selectedMaterialIds?.contains('Vật tư điện nước') ?? false) {
        quantities['Vật tư điện nước'] = double.parse(
          totalFloorArea.toStringAsFixed(2),
        );
      }
    }

    // Tính chi phí dựa trên số lượng và giá đơn vị mặc định
    Map<String, double> defaultPrices = {
      'Gạch xây': 1500, // VNĐ/viên
      'Xi măng': 1800000, // VNĐ/m³
      'Cát xây': 300000, // VNĐ/m³
      'Cát trát': 350000, // VNĐ/m³
      'Đá': 350000, // VNĐ/m³
      'Nước': 10000, // VNĐ/m³
      'Bê tông': 1200000, // VNĐ/m³
      'Thép': 20000, // VNĐ/kg
      'Nhôm': 200000, // VNĐ/kg
      'Kính': 300000, // VNĐ/m²
      'Gỗ': 15000000, // VNĐ/m³
      'Gạch men': 200000, // VNĐ/m²
      'Keo dán gạch': 5000000, // VNĐ/m³
      'Tấm thạch cao': 150000, // VNĐ/m²
      'Nhân công xây dựng': 1300000, // VNĐ/m²
      'Nhân công điện nước': 140000, // VNĐ/m²
      'Vật tư điện nước': 250000, // VNĐ/m²
      'Cát bê tông': 450000, // VNĐ/m³
    };

    // Tính chi phí cho từng loại vật liệu
    double totalMaterialCost = 0;

    for (final entry in quantities.entries) {
      final materialName = entry.key;
      final quantity = entry.value;
      final price = defaultPrices[materialName] ?? 0;

      costs[materialName] = quantity * price;
      totalMaterialCost += quantity * price;
    }

    // Tính chi phí nhân công (nếu không có trong danh sách vật liệu đã chọn)
    double laborCost = 0;
    if (!(selectedMaterialIds?.contains('Nhân công xây dựng') ?? false) &&
        !(selectedMaterialIds?.contains('Nhân công điện nước') ?? false)) {
      // Sử dụng phương pháp tính cũ (30% chi phí vật liệu)
      laborCost = totalMaterialCost * 0.3;
    }

    // Tính chi phí khác (giả định 10% tổng chi phí)
    final otherCost = totalMaterialCost * 0.1;

    // Tổng hợp kết quả cuối cùng
    return {
      'intermediateResults': intermediateResults,
      'quantities': quantities,
      'costs': costs,
      'materialCost': totalMaterialCost,
      'laborCost': laborCost,
      'otherCost': otherCost,
      'totalCost': totalMaterialCost + laborCost + otherCost,
    };
  }
}
