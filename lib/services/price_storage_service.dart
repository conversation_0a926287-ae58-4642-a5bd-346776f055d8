import 'package:shared_preferences/shared_preferences.dart';
import '../models/material_model.dart';
import '../models/custom_material.dart';

/// Dịch vụ xử lý việc lưu và tải giá vật liệu
class PriceStorageService {
  /// Tiền tố cho các khóa giá trong shared preferences
  static const String _priceKeyPrefix = 'material_price_';
  
  /// Tiền tố cho các khóa vật liệu tùy chỉnh
  static const String _customMaterialPrefix = 'custom_material_';
  
  /// Lưu giá của một vật liệu vào shared preferences
  Future<bool> saveMaterialPrice(String materialName, double price) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.setDouble('$_priceKeyPrefix$materialName', price);
  }
  
  /// Lưu tất cả giá vật liệu vào shared preferences
  Future<bool> saveAllPrices(List<Material> materials) async {
    final prefs = await SharedPreferences.getInstance();
    
    // Tạo một loạt các thao tác
    for (final material in materials) {
      await prefs.setDouble('$_priceKeyPrefix${material.name}', material.pricePerUnit);
      
      // Lưu thông tin bổ sung cho vật liệu tùy chỉnh
      if (material.type == MaterialType.custom) {
        final customMaterial = material as CustomMaterial;
        final Map<String, dynamic> customData = {
          'name': customMaterial.name,
          'pricePerUnit': customMaterial.pricePerUnit,
          'unit': customMaterial.customUnit,
          'measurementUnit': customMaterial.measurementUnit.index,
        };
        
        await prefs.setString(
          '$_customMaterialPrefix${customMaterial.name}',
          customData.toString(),
        );
      }
    }
    
    return true;
  }
  
  /// Tải giá của một vật liệu từ shared preferences
  Future<double?> loadMaterialPrice(String materialName) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getDouble('$_priceKeyPrefix$materialName');
  }
  
  /// Tải tất cả giá vật liệu từ shared preferences
  Future<Map<String, double>> loadAllPrices() async {
    final prefs = await SharedPreferences.getInstance();
    final Map<String, double> prices = {};
    
    // Lấy tất cả các khóa phù hợp với tiền tố của chúng ta
    final allKeys = prefs.getKeys();
    final priceKeys = allKeys.where((key) => key.startsWith(_priceKeyPrefix));
    
    // Tải từng giá
    for (final key in priceKeys) {
      final materialName = key.substring(_priceKeyPrefix.length);
      final price = prefs.getDouble(key);
      if (price != null) {
        prices[materialName] = price;
      }
    }
    
    return prices;
  }
  
  /// Tải tất cả vật liệu tùy chỉnh từ shared preferences
  Future<List<CustomMaterial>> loadCustomMaterials() async {
    final prefs = await SharedPreferences.getInstance();
    final List<CustomMaterial> customMaterials = [];
    
    // Lấy tất cả các khóa phù hợp với tiền tố vật liệu tùy chỉnh
    final allKeys = prefs.getKeys();
    final customKeys = allKeys.where((key) => key.startsWith(_customMaterialPrefix));
    
    // Tải từng vật liệu tùy chỉnh
    for (final key in customKeys) {
      final customDataString = prefs.getString(key);
      if (customDataString != null) {
        try {
          // Phân tích chuỗi thành Map
          final customDataString = prefs.getString(key);
          if (customDataString != null) {
            // Đây là một cách đơn giản để phân tích chuỗi, trong thực tế nên dùng json
            final name = customDataString.split('name: ')[1].split(',')[0];
            final priceString = customDataString.split('pricePerUnit: ')[1].split(',')[0];
            final unit = customDataString.split('unit: ')[1].split(',')[0];
            final measurementUnitIndexString = customDataString.split('measurementUnit: ')[1].split('}')[0];
            
            final price = double.tryParse(priceString) ?? 0.0;
            final measurementUnitIndex = int.tryParse(measurementUnitIndexString) ?? 0;
            
            customMaterials.add(
              CustomMaterial(
                name: name,
                pricePerUnit: price,
                customUnit: unit,
                measurementUnit: MeasurementUnit.values[measurementUnitIndex],
              ),
            );
          }
        } catch (e) {
          // Bỏ qua lỗi phân tích
          // Không sử dụng print trong production code
        }
      }
    }
    
    return customMaterials;
  }
}
