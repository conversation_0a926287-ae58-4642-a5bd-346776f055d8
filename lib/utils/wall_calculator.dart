import '../models/brick.dart';

/// Lớp tiện ích để tính toán vật liệu cho tường
class WallCalculator {
  /// T<PERSON>h toán các thông số cho tường dựa trên loại tường
  static Map<String, dynamic> calculateWallParameters({
    required WallType wallType,
    required double length,
    required double height,
  }) {
    // Tính diện tích tường
    final double wallArea = length * height;
    
    // Tính thể tích tường
    double wallThickness;
    switch (wallType) {
      case WallType.wall10cm1Side:
      case WallType.wall10cm2Side:
        wallThickness = 0.1; // 10cm
        break;
      case WallType.wall20cm1Side:
      case WallType.wall20cm2Side:
        wallThickness = 0.2; // 20cm
        break;
    }
    
    final double wallVolume = length * height * wallThickness;
    
    // Tính diện tích trát
    double plasteringArea;
    switch (wallType) {
      case WallType.wall10cm1Side:
      case WallType.wall20cm1Side:
        plasteringArea = wallArea; // Trát 1 mặt
        break;
      case WallType.wall10cm2Side:
      case WallType.wall20cm2Side:
        plasteringArea = wallArea * 2; // Trát 2 mặt
        break;
    }
    
    return {
      'wallType': wallType,
      'length': length,
      'height': height,
      'wallArea': wallArea,
      'wallVolume': wallVolume,
      'plasteringArea': plasteringArea,
    };
  }
}
