import 'package:flutter/material.dart';

/// Utility class để tạo các gradient từ brand color
class GradientUtils {
  /// Tạo bộ màu gradient từ brand color
  static List<Color> createGradientColorsFromBrand(
    Color brandColor, {
    double opacity = 0.7,
  }) {
    // Sử dụng HSL để tạo các biến thể màu
    final hslColor = HSLColor.fromColor(brandColor);
    
    return [
      HSLColor.fromAHSL(
        opacity,
        hslColor.hue,
        hslColor.saturation,
        hslColor.lightness + 0.1,
      ).toColor(),
      HSLColor.fromAHSL(
        opacity - 0.1,
        (hslColor.hue + 30) % 360,
        hslColor.saturation,
        hslColor.lightness,
      ).toColor(),
      HSLColor.fromAHSL(
        opacity - 0.1,
        (hslColor.hue + 60) % 360,
        hslColor.saturation - 0.1,
        hslColor.lightness,
      ).toColor(),
      HSLColor.fromAHSL(
        opacity,
        (hslColor.hue + 90) % 360,
        hslColor.saturation - 0.2,
        hslColor.lightness + 0.1,
      ).toColor(),
    ];
  }
  
  /// Tạo gradient từ primary color của theme
  static List<Color> createPrimaryGradient(BuildContext context) {
    final primaryColor = Theme.of(context).primaryColor;
    return createGradientColorsFromBrand(primaryColor);
  }
  
  /// Tạo gradient từ accent color
  static List<Color> createAccentGradient(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final accentColor = colorScheme.secondary;
    return createGradientColorsFromBrand(accentColor, opacity: 0.6);
  }
  
  /// Tạo gradient từ error color
  static List<Color> createErrorGradient(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final errorColor = colorScheme.error;
    return createGradientColorsFromBrand(errorColor, opacity: 0.7);
  }
  
  /// Tạo gradient từ success color (green)
  static List<Color> createSuccessGradient() {
    return createGradientColorsFromBrand(Colors.green, opacity: 0.6);
  }
  
  /// Tạo gradient từ warning color (amber)
  static List<Color> createWarningGradient() {
    return createGradientColorsFromBrand(Colors.amber, opacity: 0.6);
  }
  
  /// Tạo gradient từ info color (blue)
  static List<Color> createInfoGradient() {
    return createGradientColorsFromBrand(Colors.blue, opacity: 0.6);
  }
}
