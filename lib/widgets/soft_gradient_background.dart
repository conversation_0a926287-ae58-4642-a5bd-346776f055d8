import 'package:flutter/material.dart';

/// Widget có thể tái sử dụng để tạo nền gradient mềm mại
/// cho bất kỳ widget con nào
class SoftGradientBackground extends StatelessWidget {
  /// Child widget được hiển thị trên nền gradient
  final Widget? child;

  /// Cường độ của gradient (0.0 đến 1.0)
  final double intensity;

  /// BorderRadius của container nếu cần
  final BorderRadius? borderRadius;

  /// Padding bên trong container
  final EdgeInsetsGeometry? padding;

  /// Màu sắc chính của gradient, mặc định sẽ sử dụng các màu trong hình mẫu
  final List<Color>? colors;

  /// Constructor cho toàn bộ màn hình hoặc container lớn
  const SoftGradientBackground({
    super.key,
    this.child,
    this.intensity = 1.0,
    this.borderRadius,
    this.padding,
    this.colors,
  });

  /// Static method cho các widget nhỏ như card, button
  /// với tỷ lệ và cường độ phù hợp hơn
  static Widget card({
    Key? key,
    Widget? child,
    double intensity = 0.6,
    BorderRadius? borderRadius,
    EdgeInsetsGeometry? padding,
    List<Color>? colors,
  }) {
    final defaultColors = [
      Colors.purple.shade200.withAlpha((0.7 * intensity * 255).toInt()),
      Colors.pink.shade200.withAlpha((0.5 * intensity * 255).toInt()),
      Colors.orange.shade200.withAlpha((0.5 * intensity * 255).toInt()),
      Colors.amber.shade100.withAlpha((0.6 * intensity * 255).toInt()),
    ];

    return Container(
      key: key,
      padding: padding,
      decoration: BoxDecoration(
        borderRadius: borderRadius,
        gradient: RadialGradient(
          center: const Alignment(-0.5, -0.6),
          radius: 1.8,
          colors: colors ?? defaultColors,
          stops: const [0.1, 0.4, 0.7, 0.9],
        ),
      ),
      child: child,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding,
      decoration: BoxDecoration(
        borderRadius: borderRadius,
        color: Colors.white, // Fallback color
      ),
      child: ClipRRect(
        borderRadius: borderRadius ?? BorderRadius.zero,
        child: SizedBox(
          width: double.infinity,
          height: double.infinity,
          child: Stack(
            fit: StackFit.expand,
            children: [
              // Nền gradient cơ bản
              _buildBaseGradient(),

              // Overlay gradient mờ ảo
              CustomPaint(
                painter: GradientPainter(intensity: intensity, colors: colors),
              ),

              // Widget con
              if (child != null)
                Container(alignment: Alignment.center, child: child),
            ],
          ),
        ),
      ),
    );
  }

  /// Tạo gradient nền cơ bản
  Widget _buildBaseGradient() {
    final defaultColors = [
      Colors.purple.shade200.withAlpha((0.7 * intensity * 255).toInt()),
      Colors.pink.shade200.withAlpha((0.5 * intensity * 255).toInt()),
      Colors.orange.shade200.withAlpha((0.5 * intensity * 255).toInt()),
      Colors.amber.shade100.withAlpha((0.6 * intensity * 255).toInt()),
    ];

    return Container(
      decoration: BoxDecoration(
        gradient: RadialGradient(
          center: const Alignment(-0.5, -0.6),
          radius: 1.8,
          colors: colors ?? defaultColors,
          stops: const [0.1, 0.4, 0.7, 0.9],
        ),
      ),
    );
  }
}

/// Custom painter để tạo hiệu ứng mờ ảo bổ sung
class GradientPainter extends CustomPainter {
  final double intensity;
  final List<Color>? colors;

  GradientPainter({this.intensity = 1.0, this.colors});

  @override
  void paint(Canvas canvas, Size size) {
    // Gradient tím-hồng phía trên bên trái
    final paint1 =
        Paint()
          ..shader = RadialGradient(
            center: const Alignment(-0.8, -0.8),
            radius: 0.8,
            colors:
                colors?.isNotEmpty == true
                    ? [
                      colors![0].withAlpha((0.6 * intensity * 255).toInt()),
                      Colors.transparent,
                    ]
                    : [
                      Colors.purple.shade300.withAlpha(
                        (0.6 * intensity * 255).toInt(),
                      ),
                      Colors.transparent,
                    ],
          ).createShader(Rect.fromLTWH(0, 0, size.width, size.height));
    canvas.drawRect(Rect.fromLTWH(0, 0, size.width, size.height), paint1);

    // Gradient hồng-cam ở giữa
    final paint2 =
        Paint()
          ..shader = RadialGradient(
            center: const Alignment(0.3, 0.5),
            radius: 0.9,
            colors:
                (colors?.length ?? 0) >= 2
                    ? [
                      colors![1].withAlpha((0.5 * intensity * 255).toInt()),
                      Colors.transparent,
                    ]
                    : [
                      Colors.pink.shade200.withAlpha(
                        (0.5 * intensity * 255).toInt(),
                      ),
                      Colors.transparent,
                    ],
          ).createShader(Rect.fromLTWH(0, 0, size.width, size.height));
    canvas.drawRect(Rect.fromLTWH(0, 0, size.width, size.height), paint2);

    // Gradient vàng bên phải
    final paint3 =
        Paint()
          ..shader = RadialGradient(
            center: const Alignment(0.9, 0.2),
            radius: 0.7,
            colors:
                (colors?.length ?? 0) >= 3
                    ? [
                      colors![2].withAlpha((0.6 * intensity * 255).toInt()),
                      Colors.transparent,
                    ]
                    : [
                      Colors.amber.shade200.withAlpha(
                        (0.6 * intensity * 255).toInt(),
                      ),
                      Colors.transparent,
                    ],
          ).createShader(Rect.fromLTWH(0, 0, size.width, size.height));
    canvas.drawRect(Rect.fromLTWH(0, 0, size.width, size.height), paint3);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) =>
      oldDelegate is GradientPainter &&
      (oldDelegate.intensity != intensity || oldDelegate.colors != colors);
}
