# Hướng dẫn phát triển ứng dụng lập dự toán vật tư xây dựng

## Mục tiêu
Xây dựng một ứng dụng Flutter hỗ trợ lập dự toán vật tư xây dựng. Người dùng có thể chọn loại vật liệu, nhậ<PERSON> thông số, tính toán khối lượng và chi phí, đồng thời cài đặt giá cho từng loại vật liệu.

## Hướng dẫn cho AI Agent
- AI Agent phải quét và thực hiện theo các hướng dẫn trong file này (`docs/app_development_guide.md`).
- File này là tài liệu chính để triển khai ứng dụng. Mọi logic, cấu trúc mã nguồn, và giao diện đều phải tuân theo các bước dưới đây.

## C<PERSON><PERSON> trúc mã nguồn

### 1. <PERSON>ớ<PERSON> cơ sở `Material`
- **<PERSON><PERSON><PERSON><PERSON> tính**:
  - `String name`: <PERSON><PERSON><PERSON> vật li<PERSON> (ví dụ: "Gạch xây", "Cát").
  - `double pricePerUnit`: Giá mỗi đơn vị (ví dụ: VNĐ/viên, VNĐ/m³).
- **Phương thức**:
  - `double calculateQuantity(Map<String, dynamic> parameters)`: Phương thức trừu tượng tính toán khối lượng dựa trên thông số.

### 2. Các lớp vật liệu cụ thể
Mỗi lớp kế thừa từ `Material` và triển khai `calculateQuantity` theo công thức riêng.

- **Brick (Gạch xây)**:
  - **Thông số**: `length` (dài), `width` (rộng), `height` (cao) của tường.
  - **Công thức**: Số lượng gạch = (dài * rộng * cao) / (thể tích 1 viên gạch, ví dụ: 0.2m x 0.1m x 0.05m).

- **Sand (Cát)**:
  - **Thông số**: `wallVolume` (thể tích tường).
  - **Công thức**: Lượng cát = `wallVolume` * 0.02 m³ cát/m³ tường.

- **Cement (Xi măng)**:
  - **Thông số**: `wallVolume` (thể tích tường).
  - **Công thức**: Lượng xi măng = `wallVolume` * 5 kg/m³.

- **Steel (Sắt thép)**:
  - **Thông số**: `length` (chiều dài cấu kiện), `quantity` (số lượng thanh).
  - **Công thức**: Tổng chiều dài = `length` * `quantity`.

- **Stone (Đá)**:
  - **Thông số**: `volume` (thể tích).
  - **Công thức**: Lượng đá = `volume` * 1.0 m³ đá/m³.

- **Tile (Gạch ngói/Ốp lát)**:
  - **Thông số**: `surfaceArea` (diện tích bề mặt).
  - **Công thức**: Số lượng gạch = `surfaceArea` / (diện tích 1 viên gạch, ví dụ: 0.04 m²).

### 3. Quản lý danh sách vật liệu
- Tạo `List<Material>` chứa các vật liệu: `Brick`, `Sand`, `Cement`, `Steel`, `Stone`, `Tile`.

### 4. Logic tính toán
- Hiển thị các trường nhập liệu dựa trên vật liệu được chọn (ví dụ: `TextField` cho dài, rộng, cao).
- Thu thập thông số vào `Map<String, dynamic>`.
- Tính khối lượng bằng `calculateQuantity`, sau đó tính chi phí: `quantity * pricePerUnit`.

### 5. Tab cài đặt giá
- Tạo màn hình hiển thị danh sách vật liệu và trường nhập giá.
- Lưu giá vào `shared_preferences` khi người dùng cập nhật.

### 6. Giao diện người dùng
- **Tab 1**: Chọn vật liệu (dùng `DropdownButton`).
- **Tab 2**: Nhập thông số (dùng `TextField`).
- **Tab 3**: Cài đặt giá (dùng `ListView` và `TextField`).
- Đảm bảo giao diện phản hồi nhanh, dễ sử dụng.

### 7. Lưu trữ dữ liệu
- Sử dụng `shared_preferences` để lưu giá vật liệu dưới dạng key-value (ví dụ: `"brick_price": 500`).
- Tải giá khi ứng dụng khởi động.

## Hướng dẫn thực hiện

1. **Kiểm tra file này**:
   - Đảm bảo AI Agent đọc và hiểu nội dung của `docs/app_development_guide.md`.

2. **Tạo lớp `Material` và các lớp con**:
   - Định nghĩa lớp trừu tượng `Material`.
   - Tạo các lớp con và triển khai `calculateQuantity`.

3. **Thiết kế giao diện**:
   - Sử dụng `TabBar` để tạo 3 tab: chọn vật liệu, nhập thông số, cài đặt giá.
   - Thêm widget tương tác như `DropdownButton`, `TextField`.

4. **Xử lý logic tính toán**:
   - Hiển thị trường nhập liệu dựa trên vật liệu.
   - Tính toán và hiển thị kết quả (khối lượng, chi phí).

5. **Lưu trữ giá**:
   - Sử dụng `shared_preferences` để lưu và tải giá.
   - Đảm bảo giá được cập nhật khi người dùng thay đổi.

6. **Kiểm tra**:
   - Kiểm tra công thức tính toán trên dữ liệu mẫu.
   - Đảm bảo giao diện hoạt động tốt trên các kích thước màn hình.

## Lưu ý
- Thêm chú thích trong mã để giải thích công thức và logic.
- Thiết kế mã nguồn linh hoạt để dễ dàng thêm vật liệu mới hoặc chỉnh sửa công thức.
- Nếu có bất kỳ mâu thuẫn nào, ưu tiên thực hiện theo file này.