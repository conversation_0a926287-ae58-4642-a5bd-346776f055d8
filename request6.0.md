# Hướng Dẫn Triển Khai Tính Toán Khối Lượng Vật Liệu cho Ứng Dụng

## 1. Giới Thiệu
- File này cung cấp hướng dẫn chi tiết để triển khai các hàm tính toán khối lượng vật liệu xây dựng (gạch, cát, xi măng, nước) cho phần hàm tính toán trên ứng dụng hiện tại. <PERSON><PERSON><PERSON> công thức được thiết kế tổng quát, phù hợp với nhiều kích thước gạch và loại tường khác nhau (tường 10 chát 1 mặt, tường 10 chát 2 mặt, tường 20 chát 1 mặt, tường 20 chát 2 mặt), đồng thời tối ưu hóa hiệu suất và khả năng tích hợp.
- hãy tìm hiểu kỹ file hướng dẫn này trước khi triển khai. đọc hiểu từng công thức Tạo các hàm Dart tối ưu như hướng dẫn trong file này.
- Đảm bảo tính linh hoạt cho các loại tường (10, 20) và số mặt trát (1, 2).
- Tích hợp hàm tổng hợp calculateMaterials để dễ dàng sử dụng trong ứng dụng.

## 2. Thông Số Cơ Bản
- **Diện tích tường (\( A \))**:  
  \[
  A = \text{Chiều dài} \times \text{Chiều cao}
  \]
  Đơn vị: \( m^2 \).

- **Kích thước gạch**: Gạch có kích thước \( L_g \times W_g \times H_g \) (dài × rộng × cao, đơn vị: m).  
  Thể tích một viên gạch:  
  \[
  V_g = L_g \times W_g \times H_g
  \]

- **Số viên gạch trên mỗi \( m^2 \) tường**:  
  - **Tường 10**:  
    \[
    S_{10} = \frac{1}{L_g \times H_g}
    \]
  - **Tường 20**:  
    \[
    S_{20} = 2 \times S_{10}
    \]

## 3. Công Thức Tính Toán
### 3.1. Số Lượng Gạch (\( N_g \))
\[
N_g = A \times S_t
\]
- \( S_t \): Số viên gạch trên mỗi \( m^2 \), phụ thuộc loại tường:  
  - **Tường 10**: \( S_t = S_{10} \)  
  - **Tường 20**: \( S_t = S_{20} \)

### 3.2. Thể Tích Vữa Xây (\( V_x \))
\[
V_x = A \times D_t \times 0.3
\]
- \( D_t \): Độ dày tường:  
  - **Tường 10**: 0.1 m  
  - **Tường 20**: 0.2 m  
- Hệ số 0.3: Tỷ lệ vữa chiếm trong thể tích tường.

### 3.3. Thể Tích Vữa Trát (\( V_t \))
\[
V_t = A \times M_t \times 0.015
\]
- \( M_t \): Số mặt trát (1 hoặc 2).  
- 0.015 m: Độ dày lớp vữa trát mỗi mặt.

### 3.4. Khối Lượng Xi Măng (\( M_x \)) và Cát (\( M_c \))
- **Xi măng**:  
  \[
  M_x = \frac{(V_x + V_t)}{4}
  \]
- **Cát**:  
  \[
  M_c = (V_x + V_t) \times \frac{3}{4}
  \]
- Tỷ lệ 1:3 (1 phần xi măng, 3 phần cát).

### 3.5. Lượng Nước (\( W \))
\[
W = (V_x + V_t) \times 0.2
\]
- Hệ số 0.2: Tỷ lệ nước trong tổng thể tích vữa.

## 4. Triển Khai Code Tối Ưu
Dưới đây là các hàm được thiết kế để triển khai trong ứng dụng Flutter, sử dụng ngôn ngữ Dart. Các hàm được tối ưu để dễ bảo trì, tái sử dụng và tích hợp.

### 4.1. Hàm Tính Số Viên Gạch Trên Mỗi \( m^2 \)
```dart
double calculateBricksPerSquareMeter(double brickLength, double brickHeight, String wallType) {
  double bricksPerM2 = 1 / (brickLength * brickHeight);
  if (wallType == "10") {
    return bricksPerM2;
  } else if (wallType == "20") {
    return 2 * bricksPerM2;
  } else {
    throw Exception("Loại tường không hợp lệ. Chọn '10' hoặc '20'.");
  }
}

### 4.2. Hàm Tính Số Lượng Gạch 
double calculateBrickQuantity(double area, double bricksPerM2) {
  return area * bricksPerM2;
}

### 4.3. Hàm Tính Thể Tích Vữa Xây 
double calculateMixingVolume(double area, String wallType) {
  double thickness = wallType == "10" ? 0.1 : 0.2;
  return area * thickness * 0.3;
}

### 4.4. Hàm Tính Thể Tích Vữa Trát 
double calculatePlasteringVolume(double area, int plasteringSides) {
  return area * plasteringSides * 0.015;
}

### 4.5. Hàm Tính Khối Lượng Xi Măng và Cát 
Map<String, double> calculateCementAndSand(double buildingMortar, double plasteringMortar) {
  double totalMortar = buildingMortar + plasteringMortar;
  return {
    "cement": totalMortar / 4,  // Xi măng
    "sand": totalMortar * 3 / 4 // Cát
  };
}

### 4.6. Hàm Tính Lượng Nước 
double calculateWater(double buildingMortar, double plasteringMortar) {
  return (buildingMortar + plasteringMortar) * 0.2;
}

### 4.7. Hàm Tổng Hợp Tính Toán Toàn Bộ
Map<String, double> calculateMaterials({
  required double length,
  required double height,
  required double brickLength,
  required double brickHeight,
  required String wallType,
  required int plasterSides,
}) {
  // Tính diện tích tường
  double area = length * height;

  // Tính số viên gạch trên mỗi m²
  double bricksPerM2 = calculateBricksPerSquareMeter(brickLength, brickHeight, wallType);

  // Tính số lượng gạch
  double brickQuantity = calculateBrickQuantity(area, bricksPerM2);

  // Tính thể tích vữa xây
  double buildingMortar = calculateMortarVolumeForBuilding(area, wallType);

  // Tính thể tích vữa trát
  double plasteringMortar = calculateMortarVolumeForPlastering(area, plasterSides);

  // Tính xi măng và cát
  Map<String, double> cementAndSand = calculateCementAndSand(buildingMortar, plasteringMortar);

  // Tính nước
  double water = calculateWater(buildingMortar, plasteringMortar);

  // Trả về kết quả
  return {
    "bricks": brickQuantity,
    "cement": cementAndSand["cement"]!,
    "sand": cementAndSand["sand"]!,
    "water": water,
  };
}