Dưới đây là nội dung hướng dẫn về các thay đổi và cải tiến đã được thực hiện để tối ưu giao diện danh sách dự án trên `HomeScreen`.

---

# 📘 Home Screen Optimization Guide

## ✅ Tóm tắt cải tiến

Đã nâng cấp phần hiển thị danh sách dự án trong `HomeScreen` với các thay đổi chính:

### 1. 🎯 Animation vuốt mượt (Smooth Page Animation)

* Sử dụng `PageView.builder` kết hợp với `AnimatedBuilder` để tạo hiệu ứng co giãn và làm mờ nhẹ khi cuộn.
* Logic `scale`, `opacity`, `translate`, và `rotateY` được thêm để cải thiện trải nghiệm người dùng khi vuốt giữa các thẻ dự án.

```dart
double value = (1 - (pageOffset.abs() * 0.25)).clamp(0.85, 1.0);
opacity = (1 - pageOffset.abs()).clamp(0.5, 1.0);
dx = pageOffset * 30;
```

---

### 2. 🔵 Thêm chấm tròn chỉ vị trí trang (Page Indicator)

* Giao diện hiển thị các chấm nhỏ ở cuối để phản hồi vị trí trang hiện tại trong `PageView`.
* Chấm hiện tại có màu `blueAccent` và lớn hơn.

```dart
Widget _buildPageIndicator(int count)
```

---

## 🧩 Kết cấu mới của giao diện chính

```dart
Column(
  children: [
    SizedBox(
      height: 280,
      child: PageView.builder(
        controller: _pageController,
        ...
      ),
    ),
    SizedBox(height: 12),
    _buildPageIndicator(projects.length),
  ],
)
```

---

## 🔧 Hướng dẫn bảo trì cho dev khác

* Khi thêm hiệu ứng mới, chỉ cần chỉnh trong `AnimatedBuilder` phần `itemBuilder` của `PageView`.
* Để đổi màu hoặc kiểu của `PageIndicator`, chỉnh trong `_buildPageIndicator`.
* Muốn thêm thao tác khác khi người dùng vuốt sang trang khác, chỉnh trong `onPageChanged`.

---

## 📄 Code tham khảo

```dart

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/project.dart';
import '../providers/project_provider.dart';
import '../screens/project_detail_screen.dart';
import '../widgets/gradient_card.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _currentPage = 0;
  late final PageController _pageController;

  @override
  void initState() {
    super.initState();
    _pageController = PageController(viewportFraction: 0.85);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  Widget _buildEmptyProjectState() {
    return const Center(
      child: Text(
        'Không có dự án nào. Hãy thêm dự án mới!',
        style: TextStyle(fontSize: 16),
      ),
    );
  }

  void _showProjectOptionsBottomSheet(BuildContext context, Project project, ProjectProvider provider) {
    showModalBottomSheet(
      context: context,
      builder: (_) {
        return SafeArea(
          child: Wrap(
            children: [
              ListTile(
                leading: const Icon(Icons.delete),
                title: const Text('Xóa dự án'),
                onTap: () {
                  provider.deleteProject(project);
                  Navigator.of(context).pop();
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPageIndicator(int count) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(
        count,
        (index) => AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          margin: const EdgeInsets.symmetric(horizontal: 4),
          width: _currentPage == index ? 12 : 8,
          height: _currentPage == index ? 12 : 8,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: _currentPage == index ? Colors.blueAccent : Colors.grey,
          ),
        ),
      ),
    );
  }

  Widget _buildProjectList() {
    return Consumer<ProjectProvider>(
      builder: (context, projectProvider, child) {
        final projects = projectProvider.projects;

        if (projects.isEmpty) {
          return _buildEmptyProjectState();
        }

        return Column(
          children: [
            SizedBox(
              height: 280,
              child: PageView.builder(
                controller: _pageController,
                itemCount: projects.length,
                onPageChanged: (page) {
                  setState(() {
                    _currentPage = page;
                  });
                },
                itemBuilder: (context, index) {
                  final project = projects[index];

                  return AnimatedBuilder(
                    animation: _pageController,
                    builder: (context, child) {
                      double value = 1.0;
                      double opacity = 1.0;
                      double dx = 0;

                      if (_pageController.position.haveDimensions) {
                        final pageOffset = _pageController.page! - index;
                        value = (1 - (pageOffset.abs() * 0.25)).clamp(0.85, 1.0);
                        opacity = (1 - pageOffset.abs()).clamp(0.5, 1.0);
                        dx = pageOffset * 30;
                      }

                      return Opacity(
                        opacity: opacity,
                        child: Transform(
                          alignment: Alignment.center,
                          transform: Matrix4.identity()
                            ..translate(dx)
                            ..scale(value, value)
                            ..setEntry(3, 2, 0.001)
                            ..rotateY((_pageController.page! - index) * 0.05),
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 12),
                            child: GestureDetector(
                              onTap: () {
                                projectProvider.selectProject(project);
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (_) => ProjectDetailScreen(project: project),
                                  ),
                                );
                              },
                              onLongPress: () {
                                _showProjectOptionsBottomSheet(
                                  context,
                                  project,
                                  projectProvider,
                                );
                              },
                              child: GradientCard(
                                title: project.name,
                                subtitle:
                                    '${project.location} • ${project.createdAt.day}/${project.createdAt.month}/${project.createdAt.year}',
                                icon: Icons.home_work,
                                width: double.infinity,
                                height: 230,
                                onTap: () {
                                  projectProvider.selectProject(project);
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (_) => ProjectDetailScreen(project: project),
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  );
                },
              ),
            ),
            const SizedBox(height: 12),
            _buildPageIndicator(projects.length),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Trang chủ'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Dự án của bạn',
              style: TextStyle(
                fontSize: 22,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            _buildProjectList(),
          ],
        ),
      ),
    );
  }
}

---