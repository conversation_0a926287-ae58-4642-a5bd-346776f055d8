# Hướng Dẫn và Prompt cho AI Agent: Tố<PERSON> Ưu Ứng Dụng lập dự toán xây nhà cập nhật lần 3

## 1. <PERSON><PERSON><PERSON> tiêu cải tiến
Ứng dụng lập dự toán xây nhà được cải tiến lần 3. <PERSON><PERSON><PERSON> tiêu tập trung vào tối ưu hóa trải nghiệm người dùng (UX) thông qua giao diện (UI) rõ ràng, trực quan và thân thiện. Hướng dẫn này cung cấp chi tiết về luồng người dùng, yê<PERSON> cầu thiết kế, và các tính năng cần triển khai để AI Agent có thể thực hiện tối ưu hóa ứng dụng theo đúng mục tiêu đề ra.

## 2. <PERSON><PERSON><PERSON> (User Flow)
AI Agent cần triển khai luồng người dùng theo các bước cụ thể dưới đây:

### 2.1. <PERSON><PERSON><PERSON> Sách Công Trình
- **Chứ<PERSON> năng**: Hiển thị danh sách các dự án đã tạo.
- **Thiết kế**:
  - Mỗi dự án hiển thị với ảnh đại diện hoặc biểu tượng mặc định.
  - Sử dụng **Floating Action Button (FAB)** với biểu tượng "+" để thêm dự án mới.
- **Tương tác**:
  - Vuốt sang trái/phải để xóa hoặc chỉnh sửa dự án.
  - Chạm lâu vào dự án để mở menu ngữ cảnh với các tùy chọn: đổi tên, sao chép dự án.

### 2.2. Wizard Tạo Dự Án
- **Cấu trúc**: Quy trình tạo dự án được chia thành 4 bước, sử dụng **progress indicator** để hiển thị tiến độ.
- **Chi tiết từng bước**:
  - **Bước 1: Thông Tin Cơ Bản**
    - Trường nhập: Tên công trình, địa điểm (sử dụng `TextField`).
    - Tùy chọn: Thêm ảnh minh họa bằng `Image Picker`.
    - Gợi ý: Tích hợp Google Places Autocomplete cho địa điểm.
  - **Bước 2: Chọn Loại Móng & Kết Cấu**
    - Hiển thị: Các card cuộn ngang (horizontal scrollable cards) với icon minh họa (móng băng, móng đơn, móng cọc).
    - Tương tác: Highlight card được chọn; nút "Chi Tiết" mở bottom sheet với ảnh lớn.
  - **Bước 3: Chọn Loại Vật Liệu Chính**
    - Hiển thị: Grid hoặc danh sách vật liệu (Gạch, Cát, Xi Măng) với khả năng lọc.
    - Thông tin mỗi item: Ảnh nhỏ, tên, kích thước, đơn vị, giá tham khảo.
    - Tính năng: "Yêu Thích" để lưu vật liệu thường dùng.
  - **Bước 4: Nhập Thông Số Chi Tiết**
    - Form động: Hiển thị/ẩn trường input dựa trên lựa chọn trước đó.
    - Công cụ: Slider cho chiều cao, số tầng; hỗ trợ đơn vị m², m³ với chuyển đổi tự động.
    - Kiểm tra: Inline validation để báo lỗi tức thì khi nhập liệu.

### 2.3. Màn Hình Kết Quả & Chỉnh Sửa
- **Hiển thị**:
  - Biểu đồ bánh (pie chart) thể hiện tỷ trọng chi phí theo nhóm vật liệu.
  - Danh sách chi tiết: Sử dụng bảng hoặc `ExpansionPanelList` với thông tin khối lượng, đơn giá, thành tiền.
- **Tương tác**:
  - Nút "Sửa" bên cạnh mỗi mục để chỉnh sửa nhanh.
  - Nút chức năng: "Xuất PDF", "Chia Sẻ", "Lưu Lại" để xử lý kết quả.

## 3. Tối Ưu Trải Nghiệm Nhập Liệu
- **Progress Indicator**: Hiển thị bước hiện tại và số bước còn lại rõ ràng.
- **Tự Động Nhảy**: Chuyển focus sang trường tiếp theo sau khi nhập xong.
- **Gợi Ý**: Cung cấp giá trị mặc định dựa trên công trình mẫu.
- **Hỗ Trợ Inline**: Icon "?" mở tooltip/bottom sheet giải thích công thức và thông số.
- **Kiểm Tra Lỗi**: Thông báo lỗi tức thì ngay tại trường nhập liệu.

## 4. Quản Lý Thư Viện Vật Liệu
- **Vị trí**: Đặt trong phần Settings.
- **Chức năng**:
  - Thêm/sửa vật liệu thủ công; nhập dữ liệu từ CSV.
  - **Import/Export**: Xuất/nhập danh sách vật liệu qua CSV/Excel.
- **Tìm Kiếm & Lọc**: Lọc theo tên, nhóm vật liệu, hoặc khoảng giá (range slider).

## 5. Trải Nghiệm Trực Quan & Phản Hồi
- **Feedback Tức Thì**: Cập nhật kết quả dự toán realtime khi thay đổi thông số.
- **Hiệu Ứng**: Sử dụng animation nhẹ (slide, fade) khi chuyển bước.
- **Dark Mode**: Hỗ trợ giao diện tối, tự động điều chỉnh màu sắc.
- **Offline-First**: Lưu dữ liệu tạm bằng SQLite/Hive, đồng bộ khi có mạng.

## 6. Lưu Ý
- Đảm bảo mã nguồn Flutter sạch sẽ, tuân theo nguyên tắc **DRY** (Don't Repeat Yourself).
- Sử dụng các package Flutter phổ biến như `provider` hoặc `flutter_bloc` để quản lý trạng thái.
- Giao diện ứng dụng làm theo như mô phỏng tại hình ảnh có đường dẫn sau: "assets/demo/ux_app.png"
