# Hướng Dẫn chỉnh sửa tối ưu Ứng Dụng lần thứ 4

## 1. Giới Thiệu
Ở những lần chỉnh sửa trước chúng ta đã tạo ra được một bộ khung cho ứng dụng lập dự toán xây dựng tương đối ổn về mặt hiệu năng và giao diện. Ở lần chỉnh sửa tối ưu này chúng ta sẽ tập trung tối ưu luồng Wizard 4 bước để người dùng nhập liệu dễ dàng và ứng dụng tính toán chính xác hơn. File markdown này cung cấp hướng dẫn chi tiết về luồng trải nghiệm người dùng (UX) và các yêu cầu cụ thể để AI agent triển khai ứng dụng.

## 2. Luồng UX với Wizard 4 Bước

### Bước 1: Nhậ<PERSON> Thông Tin Diện Tích và Chiều Cao
- **Mục đích**: <PERSON><PERSON> thập thông tin cơ bản về diện tích và chiều cao của các tầng và mái.
- **Thông tin cần nhập**:
  - Diện tích tầng 1 (m²).
  - Nút "Thêm Tầng" để nhập diện tích và chiều cao các tầng tiếp theo.
  - Nút "Thêm Mái" để chọn loại mái (mái bằng, mái tôn, mái ngói) và nhập diện tích sàn mái.
- **Tối ưu UX**:
  - Sử dụng trường nhập số với validation (không cho phép giá trị âm hoặc chuỗi không hợp lệ).
  - Hiển thị tổng quan các tầng và mái đã nhập dưới dạng danh sách hoặc bảng.

### Bước 2: Lựa Chọn Loại Móng và Kết Cấu
- **Mục đích**: Xác định loại móng phù hợp với công trình.
- **Thông tin cần nhập**:
  - Các tùy chọn: Móng băng, móng cọc, móng cốc, móng bè.
- **Tối ưu UX**:
  - Sử dụng radio buttons hoặc dropdown để chọn loại móng.
  - Cung cấp tooltip hoặc pop-up giải thích ngắn gọn về từng loại móng.

### Bước 3: Lựa Chọn Vật Liệu
- **Mục đích**: Chọn vật liệu xây dựng từ thư viện có sẵn.
- **Thông tin cần nhập**:
  - Danh sách vật liệu phân theo nhóm: xây thô (gạch, xi măng), hoàn thiện (sơn, gạch ốp), điện nước (dây điện, ống nước).
- **Tối ưu UX**:
  - Sử dụng checkbox hoặc multi-select dropdown để chọn nhiều vật liệu.
  - Thêm tính năng tìm kiếm và lọc vật liệu theo tên hoặc nhóm.

### Bước 4: Nhập Thông Số Chi Tiết
- **Mục đích**: Thu thập thông số kỹ thuật chi tiết để tính toán khối lượng và chi phí.
- **Thông tin cần nhập**:
  - **Móng**: Chiều dài (m), số cột trụ (nếu có).
  - **Tường**: Diện tích các loại tường (m²).
  - **Cửa**: Diện tích cửa sổ, cửa đi, cửa cuốn (m²).
  - **Khác**: Nhà vệ sinh (số lượng), trần thạch cao (m²), cầu thang (số bậc).
- **Tối ưu UX**:
  - Trường nhập số kèm đơn vị rõ ràng (m, m²).
  - Cung cấp giá trị mặc định hoặc gợi ý dựa trên diện tích đã nhập.

## 3. Tính Toán và Xuất Kết Quả
- **Quy trình tính toán**:
  - Tính khối lượng nhân công và vật liệu dựa trên thông tin từ 4 bước trên.
  - Tính chi phí tổng dựa trên khối lượng và đơn giá (đơn giá có thể lấy từ cơ sở dữ liệu mặc định hoặc nhập tay).
- **Xuất kết quả**:
  - **Biểu đồ**: Pie chart hiển thị tỷ trọng chi phí (nhân công, vật liệu, khác).
  - **Bảng chi tiết**: Liệt kê khối lượng, đơn giá, thành tiền; kèm nút "Sửa" để chỉnh sửa từng mục.
  - **Tùy chọn**: Xuất file PDF, chia sẻ qua email, hoặc lưu trữ local.

## 4. Tối Ưu Trải Nghiệm Nhập Liệu
- Sử dụng giao diện wizard chia thành 4 bước rõ ràng, có nút "Tiếp tục" và "Quay lại".
- Validation tức thời: Hiển thị thông báo lỗi ngay khi người dùng nhập sai.
- Gợi ý giá trị mặc định để giảm thời gian nhập liệu.
- Thư viện vật liệu có tính năng tìm kiếm nhanh và lọc theo nhóm.