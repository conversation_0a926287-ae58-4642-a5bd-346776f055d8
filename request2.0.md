# Hướng dẫn cập nhật ứng dụng lập dự toán vật tư xây dựng - cập nh<PERSON>t lần 2

## M<PERSON><PERSON> tiêu cải tiến
1. <PERSON><PERSON> sung các lớp vật liệu mới cần thiết cho công trình
2. <PERSON><PERSON><PERSON> thiện UX/UI một số tính năng
3. <PERSON><PERSON><PERSON> các lỗi hiện có
4. Thay đổi logic tính toán cơ bản

## 1. Cập nhật đơn giá vật liệu
- [ ] Xi măng: chuy<PERSON>n sang tính theo **tấn**
- [ ] Sắt thép: chuy<PERSON>n sang tính theo **tấn**

## 2. Thay đổi logic tính toán chính
- [ ] Implement hệ thống tính toán thông minh:
  - Tự động tính toán vật liệu khi người dùng nhập thông số cơ bản
  - Ví dụ:
    - T<PERSON><PERSON><PERSON> 10cm chát 1 mặt → tự tính gạch, c<PERSON><PERSON> x<PERSON><PERSON>, c<PERSON><PERSON> trát
    - Tường 10cm chát 2 mặt → t<PERSON>h toán tương tự
    - Tường 20cm chát 1 mặt → logic tương ứng
    - Tường 20cm chát 2 mặt → logic tương ứng

## 3. Danh mục vật liệu cần bổ sung

### Vật liệu tính theo m²:
- [ ] Ngói Tây
- [ ] Tôn thường
- [ ] Tôn xốp
- [ ] Thạch cao
- [ ] Sơn ngoại thất
- [ ] Sơn nội thất
- [ ] Cửa nhôm hệ Xingfa
- [ ] Nhân công xây dựng
- [ ] Nhân công điện nước

### Vật liệu tính theo đơn vị khác:
- [ ] Cửa nhựa composite (tính theo bộ)
- [ ] Vật tư điện nước (tính theo gói)

## 4. Danh sách lỗi cần sửa
- [ ] Hiển thị khối lượng vật liệu:
  - Làm tròn số
  - Định dạng phần nghìn (ví dụ: 1,000.50)
- [ ] Hiển thị thành tiền:
  - Định dạng phần nghìn

## 5. Cải tiến giao diện người dùng

### Tab vật liệu:
- [ ] Đổi tên thành "Thêm nhà"
- [ ] Thiết kế lại logic hiển thị tab

### Tab cài đặt giá:
- [ ] Thêm tính năng cho phép người dùng tự thêm vật liệu mới
- [ ] Logic thuộc tính thông minh:
  - Nếu chọn tính theo m² → hiển thị trường nhập chiều dài, chiều cao
  - Nếu chọn tính theo khối → hiển thị trường nhập chiều dài, sâu, cao
  - Nếu chọn đơn vị: cái/gói/bộ/kg/tấn → hiển thị trường nhập số lượng

## Lộ trình thực hiện
1. Cập nhật logic tính toán cơ bản
2. Bổ sung danh mục vật liệu mới
3. Sửa các lỗi hiện có
4. Triển khai cải tiến giao diện
5. Kiểm thử toàn hệ thống