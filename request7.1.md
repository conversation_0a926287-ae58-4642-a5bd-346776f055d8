# Hướng dẫn cải tiến file `step5_detailed_parameters.dart`

## <PERSON><PERSON><PERSON> tiêu
<PERSON> tiến file `step5_detailed_parameters.dart` để tăng tính linh hoạt cho người dùng khi nhập thông số xây dựng. <PERSON><PERSON><PERSON> tại, c<PERSON>c thông số (móng, t<PERSON><PERSON><PERSON>, nh<PERSON> vệ sinh, c<PERSON><PERSON>, th<PERSON>ch cao, c<PERSON>u thang) chỉ cho phép nhập một lần duy nhất. Các cải tiến sẽ cho phép nhập nhiều thông số hơn thông qua việc thêm các controller động và tính toán tự động dựa trên dữ liệu người dùng nhập.

Dưới đây là các yêu cầu cụ thể và hướng dẫn triển khai cho từng phần.

---

## <PERSON><PERSON><PERSON> cầu cải tiến


### 1. Th<PERSON><PERSON> số cửa
- **Hiện tại**: <PERSON><PERSON><PERSON><PERSON> dùng nhập diện tích cửa (m²) một lần duy nhất.
- **Yêu cầu cải tiến**:
  - Thay vì nhập diện tích, người dùng nhập:
    - Chiều rộng (m).
    - Chiều cao (m).
    - Số lượng (cửa/cửa sổ).
  - Cho phép thêm nhiều controller để nhập thông số cho nhiều cửa khác nhau.
- **Hướng dẫn triển khai**:
  1. Tạo danh sách động (`List<Map<String, dynamic>>`) để lưu thông số mỗi cửa, bao gồm:
     - Chiều rộng (`width`).
     - Chiều cao (`height`).
     - Số lượng (`quantity`).
  2. Thêm giao diện với:
     - Các ô nhập liệu cho chiều rộng, chiều cao, số lượng.
     - Nút "Thêm cửa" để tạo bộ controller mới.
  3. Tính diện tích từng cửa: `area = width * height * quantity`.
  4. Cập nhật logic trong `calculateMaterialsFromDetailedParams` (phần xử lý cửa) để tính tổng diện tích cửa và vật liệu (nhôm, kính) dựa trên tất cả các cửa được nhập.
  5. Đảm bảo kiểm tra giá trị đầu vào hợp lệ.

### 2. Thông số nhà vệ sinh
- **Hiện tại**: Người dùng nhập diện tích nhà vệ sinh (m²) một lần duy nhất.
- **Yêu cầu cải tiến**:
  - Cho phép thêm nhiều controller để nhập diện tích cho nhiều nhà vệ sinh.
- **Hướng dẫn triển khai**:
  1. Tạo danh sách động (`List<TextEditingController>`) để lưu các controller cho diện tích nhà vệ sinh.
  2. Thêm giao diện với:
     - Ô nhập liệu cho diện tích (m²).
     - Nút "Thêm nhà vệ sinh" để tạo controller mới.
  3. Tổng hợp diện tích tất cả nhà vệ sinh và truyền vào `detailedParams` để tính vật liệu (nếu có logic riêng cho nhà vệ sinh trong `wall_calculator.dart`).
  4. Nếu không có logic riêng, giả định vật liệu tương tự tường (tường 10, chát 2 mặt) và gọi `calculateMaterials` với diện tích tổng.

### 3. Thông số thạch cao
- **Hiện tại**: Người dùng nhập diện tích thạch cao (m²) một lần duy nhất.
- **Yêu cầu cải tiến**:
  - Cho phép thêm nhiều controller để nhập diện tích cho nhiều khu vực thạch cao.
- **Hướng dẫn triển khai**:
  1. Tạo danh sách động (`List<TextEditingController>`) để lưu các controller cho diện tích thạch cao.
  2. Thêm giao diện với:
     - Ô nhập liệu cho diện tích (m²).
     - Nút "Thêm thạch cao" để tạo controller mới.
  3. Tổng hợp diện tích thạch cao và thêm vào `detailedParams` dưới key `'ceiling'` hoặc tương tự.
  4. Cập nhật `calculateMaterialsFromDetailedParams` để tính vật liệu thạch cao (giả định: khung thép, tấm thạch cao) nếu cần.

### 4. Thông số cầu thang
- **Hiện tại**: Người dùng nhập số bậc cầu thang một lần duy nhất.
- **Yêu cầu cải tiến**:
  - Cho phép thêm nhiều controller để nhập số bậc cho nhiều cầu thang.
- **Hướng dẫn triển khai**:
  1. Tạo danh sách động (`List<TextEditingController>`) để lưu các controller cho số bậc cầu thang.
  2. Thêm giao diện với:
     - Ô nhập liệu cho số bậc.
     - Nút "Thêm cầu thang" để tạo controller mới.
  3. Tổng hợp số bậc từ tất cả cầu thang và thêm vào `detailedParams` dưới key `'stairs'` hoặc tương tự.
  4. Cập nhật `calculateMaterialsFromDetailedParams` để tính vật liệu cầu thang (giả định: bê tông, thép) dựa trên số bậc (có thể dùng hệ số cố định, ví dụ: 0.1m³ bê tông/bậc).

---

## Lưu ý chung khi triển khai

1. **Kiểm tra đầu vào**:
   - Đảm bảo tất cả giá trị nhập (chiều dài, chiều cao, diện tích, số lượng, số bậc) là số dương và hợp lệ.
   - Hiển thị thông báo lỗi nếu dữ liệu không hợp lệ.

2. **Giao diện người dùng (UI)**:
   - Sử dụng `ListView` hoặc `Column` với `TextField` để hiển thị các ô nhập liệu động.
   - Thêm nút "Thêm" (ví dụ: "Thêm móng", "Thêm tường") và nút "Xóa" để quản lý danh sách controller.
   - Cập nhật UI ngay khi thêm/xóa controller để phản ánh thay đổi.

3. **Tích hợp với `wall_calculator.dart`**:
   - Đảm bảo các thông số nhập vào khớp với các tham số yêu cầu của `calculateMaterials` và `calculateMaterialsFromDetailedParams`.
   - Tổng hợp kết quả (số lượng gạch, xi măng, cát, nước, v.v.) từ tất cả các phần (móng, tường, cửa, v.v.) để trả về người dùng.

4. **Cấu trúc dữ liệu**:
   - Cập nhật `detailedParams` để lưu trữ thông tin từ nhiều controller. Ví dụ:
     ```dart
     Map<String, dynamic> detailedParams = {
       'foundation': [
         {'length': 10.0},
         {'length': 15.0},
       ],
       'walls': [
         {'type': '10', 'plasterSides': 2, 'length': 5.0, 'height': 3.0},
         {'type': '20', 'plasterSides': 1, 'length': 4.0, 'height': 2.5},
       ],
       'doors': [
         {'width': 0.8, 'height': 2.0, 'quantity': 2},
         {'width': 1.2, 'height': 1.8, 'quantity': 1},
       ],
       // Tương tự cho nhà vệ sinh, thạch cao, cầu thang
     };